{"Comfy-Desktop.AutoUpdate": true, "Comfy-Desktop.SendStatistics": false, "Comfy.ColorPalette": "dark", "Comfy.UseNewMenu": "Top", "Comfy.Workflow.WorkflowTabsPosition": "Topbar", "Comfy.Workflow.ShowMissingModelsWarning": true, "Comfy.Server.LaunchArgs": {}, "Comfy-Desktop.UV.PythonInstallMirror": "https://github.com/astral-sh/python-build-standalone/releases/download", "Comfy-Desktop.UV.PypiInstallMirror": "https://pypi.org/simple/", "Comfy-Desktop.UV.TorchInstallMirror": "https://download.pytorch.org/whl/nightly/cpu", "Comfy.TutorialCompleted": true}