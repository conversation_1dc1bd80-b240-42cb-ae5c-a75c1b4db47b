## ComfyUI-Manager: installing dependencies done.
[2025-07-31 06:30:45.747] ** ComfyUI startup time: 2025-07-31 06:30:45.747
[2025-07-31 06:30:45.747] ** Platform: Darwin
[2025-07-31 06:30:45.747] ** Python version: 3.12.9 (main, Feb 12 2025, 15:09:19) [Clang 19.1.6 ]
[2025-07-31 06:30:45.747] ** Python executable: /Users/<USER>/Documents/ComfyUI/.venv/bin/python
[2025-07-31 06:30:45.747] ** ComfyUI Path: /Applications/ComfyUI.app/Contents/Resources/ComfyUI
[2025-07-31 06:30:45.747] ** ComfyUI Base Folder Path: /Applications/ComfyUI.app/Contents/Resources/ComfyUI
[2025-07-31 06:30:45.747] ** User directory: /Users/<USER>/Documents/ComfyUI/user
[2025-07-31 06:30:45.747] ** ComfyUI-Manager config path: /Users/<USER>/Documents/ComfyUI/user/default/ComfyUI-Manager/config.ini
[2025-07-31 06:30:45.747] ** Log path: /Users/<USER>/Documents/ComfyUI/user/comfyui.log
[ComfyUI-Manager] 'numpy' dependency were fixed
[2025-07-31 06:30:47.762] [ComfyUI-Manager] Failed to restore comfyui-frontend-package
[2025-07-31 06:30:47.762] expected str, bytes or os.PathLike object, not NoneType
[2025-07-31 06:30:47.762] 
Prestartup times for custom nodes:
[2025-07-31 06:30:47.762]    5.9 seconds: /Applications/ComfyUI.app/Contents/Resources/ComfyUI/custom_nodes/ComfyUI-Manager
[2025-07-31 06:30:47.762] 
[2025-07-31 06:30:56.228] Checkpoint files will always be loaded safely.
[2025-07-31 06:30:56.707] Total VRAM 24576 MB, total RAM 24576 MB
[2025-07-31 06:30:56.707] pytorch version: 2.9.0.dev20250730
[2025-07-31 06:30:56.710] Mac Version (15, 5)
[2025-07-31 06:30:56.710] Set vram state to: SHARED
[2025-07-31 06:30:56.710] Device: mps
[2025-07-31 06:31:21.264] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-31 06:31:50.297] Python version: 3.12.9 (main, Feb 12 2025, 15:09:19) [Clang 19.1.6 ]
[2025-07-31 06:31:50.298] ComfyUI version: 0.3.47
[2025-07-31 06:31:50.299] [Prompt Server] web root: /Applications/ComfyUI.app/Contents/Resources/ComfyUI/web_custom_versions/desktop_app
[2025-07-31 06:31:52.040] ### Loading: ComfyUI-Manager (V3.30.4)
[2025-07-31 06:31:52.040] [ComfyUI-Manager] network_mode: public
[2025-07-31 06:31:52.040] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-07-31 06:31:52.051] 
Import times for custom nodes:
[2025-07-31 06:31:52.051]    0.0 seconds: /Applications/ComfyUI.app/Contents/Resources/ComfyUI/custom_nodes/websocket_image_save.py
[2025-07-31 06:31:52.051]    0.0 seconds: /Applications/ComfyUI.app/Contents/Resources/ComfyUI/custom_nodes/ComfyUI-Manager
[2025-07-31 06:31:52.052] 
[2025-07-31 06:31:53.759] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-31 06:31:53.900] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-31 06:31:53.987] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-31 06:31:54.073] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-31 06:31:54.103] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-31 06:31:54.464] Failed to initialize database. Please ensure you have installed the latest requirements. If the error persists, please report this as in future the database will be required: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
[2025-07-31 06:31:54.469] Starting server

[2025-07-31 06:31:54.469] To see the GUI go to: http://127.0.0.1:8000
[2025-07-31 06:31:56.114] comfyui-frontend-package not found in requirements.txt
[2025-07-31 06:31:59.414] FETCH ComfyRegistry Data: 5/93
[2025-07-31 06:32:04.363] FETCH ComfyRegistry Data: 10/93
[2025-07-31 06:32:09.184] FETCH ComfyRegistry Data: 15/93
[2025-07-31 06:32:14.116] FETCH ComfyRegistry Data: 20/93
[2025-07-31 06:32:18.916] FETCH ComfyRegistry Data: 25/93
[2025-07-31 06:32:23.620] FETCH ComfyRegistry Data: 30/93
[2025-07-31 06:32:29.042] FETCH ComfyRegistry Data: 35/93
[2025-07-31 06:32:35.335] FETCH ComfyRegistry Data: 40/93
[2025-07-31 06:32:40.475] FETCH ComfyRegistry Data: 45/93
[2025-07-31 06:32:45.329] FETCH ComfyRegistry Data: 50/93
[2025-07-31 06:32:51.280] FETCH ComfyRegistry Data: 55/93
[2025-07-31 06:32:56.744] FETCH ComfyRegistry Data: 60/93
[2025-07-31 06:33:04.061] FETCH ComfyRegistry Data: 65/93
[2025-07-31 06:33:10.280] FETCH ComfyRegistry Data: 70/93
[2025-07-31 06:33:15.165] FETCH ComfyRegistry Data: 75/93
[2025-07-31 06:33:21.903] FETCH ComfyRegistry Data: 80/93
[2025-07-31 06:33:26.863] FETCH ComfyRegistry Data: 85/93
[2025-07-31 06:33:31.913] FETCH ComfyRegistry Data: 90/93
[2025-07-31 06:33:35.306] FETCH ComfyRegistry Data [DONE]
[2025-07-31 06:33:35.378] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-31 06:33:35.471] nightly_channel: 
https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/remote
[2025-07-31 06:33:35.472] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-31 06:33:35.589] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-31 06:34:03.652] got prompt
[2025-07-31 06:34:03.974] model weight dtype torch.float16, manual cast: None
[2025-07-31 06:34:03.982] model_type EPS
[2025-07-31 06:34:06.518] Using split attention in VAE
[2025-07-31 06:34:06.534] Using split attention in VAE
[2025-07-31 06:34:07.241] VAE load device: mps, offload device: cpu, dtype: torch.bfloat16
[2025-07-31 06:34:07.287] Requested to load SD1ClipModel
[2025-07-31 06:34:07.289] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-31 06:34:07.290] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-31 06:34:07.629] loaded diffusion model directly to GPU
[2025-07-31 06:34:07.629] Requested to load BaseModel
[2025-07-31 06:34:07.637] loaded completely 9.5367431640625e+25 1639.406135559082 True
[2025-07-31 06:34:09.739] Requested to load Gligen
[2025-07-31 06:34:09.974] loaded completely 9.5367431640625e+25 398.8292236328125 True
[2025-07-31 06:34:26.700] got prompt
[2025-07-31 06:36:39.374] 
100%|██████████| 20/20 [02:29<00:00,  6.92s/it]
100%|██████████| 20/20 [02:29<00:00,  7.45s/it]
[2025-07-31 06:36:39.555] Requested to load AutoencoderKL
[2025-07-31 06:36:41.726] loaded completely 9.5367431640625e+25 159.55708122253418 True
[2025-07-31 06:36:46.719] Prompt executed in 163.04 seconds
[2025-07-31 06:36:47.278] Requested to load Gligen
[2025-07-31 06:36:47.278] Requested to load BaseModel
[2025-07-31 06:36:47.600] loaded completely 9.5367431640625e+25 398.8292236328125 True
[2025-07-31 06:36:48.335] loaded completely 9.5367431640625e+25 1639.406135559082 True
[2025-07-31 06:39:07.961] 
100%|██████████| 20/20 [02:19<00:00,  6.75s/it]
100%|██████████| 20/20 [02:19<00:00,  6.98s/it]
[2025-07-31 06:39:08.004] Requested to load AutoencoderKL
[2025-07-31 06:39:11.179] loaded completely 9.5367431640625e+25 159.55708122253418 True
[2025-07-31 06:39:13.600] Prompt executed in 146.33 seconds
[2025-07-31 06:39:35.502] got prompt
[2025-07-31 06:39:36.290] Requested to load Gligen
[2025-07-31 06:39:36.646] loaded completely 9.5367431640625e+25 398.8292236328125 True
[2025-07-31 06:39:37.307] loaded completely 9.5367431640625e+25 1639.406135559082 True
[2025-07-31 06:41:49.929] 
100%|██████████| 20/20 [02:12<00:00,  7.00s/it]
100%|██████████| 20/20 [02:12<00:00,  6.63s/it]
[2025-07-31 06:41:49.991] Requested to load AutoencoderKL
[2025-07-31 06:41:53.472] loaded completely 9.5367431640625e+25 159.55708122253418 True
[2025-07-31 06:41:55.924] Prompt executed in 140.41 seconds
[2025-07-31 06:45:03.029] comfyui-frontend-package not found in requirements.txt
[2025-07-31 06:48:09.309] got prompt
[2025-07-31 06:48:09.314] Failed to validate prompt for output 58:
[2025-07-31 06:48:09.315] * VAELoader 39:
[2025-07-31 06:48:09.315]   - Value not in list: vae_name: 'wan2.2_vae.safetensors' not in []
[2025-07-31 06:48:09.315] * UNETLoader 37:
[2025-07-31 06:48:09.315]   - Value not in list: unet_name: 'wan2.2_ti2v_5B_fp16.safetensors' not in []
[2025-07-31 06:48:09.315] * CLIPLoader 38:
[2025-07-31 06:48:09.315]   - Value not in list: clip_name: 'umt5_xxl_fp8_e4m3fn_scaled.safetensors' not in []
[2025-07-31 06:48:09.315] Output will be ignored
[2025-07-31 06:48:09.315] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
