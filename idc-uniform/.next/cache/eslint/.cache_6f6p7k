[{"/Users/<USER>/Documents/idc-uniform/src/app/about/page.tsx": "1", "/Users/<USER>/Documents/idc-uniform/src/app/contact/page.tsx": "2", "/Users/<USER>/Documents/idc-uniform/src/app/layout.tsx": "3", "/Users/<USER>/Documents/idc-uniform/src/app/loading.tsx": "4", "/Users/<USER>/Documents/idc-uniform/src/app/not-found.tsx": "5", "/Users/<USER>/Documents/idc-uniform/src/app/page.tsx": "6", "/Users/<USER>/Documents/idc-uniform/src/app/products/[id]/page.tsx": "7", "/Users/<USER>/Documents/idc-uniform/src/app/products/page.tsx": "8", "/Users/<USER>/Documents/idc-uniform/src/app/robots.ts": "9", "/Users/<USER>/Documents/idc-uniform/src/app/services/page.tsx": "10", "/Users/<USER>/Documents/idc-uniform/src/app/sitemap.ts": "11", "/Users/<USER>/Documents/idc-uniform/src/components/category-card.tsx": "12", "/Users/<USER>/Documents/idc-uniform/src/components/footer.tsx": "13", "/Users/<USER>/Documents/idc-uniform/src/components/navigation.tsx": "14", "/Users/<USER>/Documents/idc-uniform/src/components/placeholder-image.tsx": "15", "/Users/<USER>/Documents/idc-uniform/src/components/product-card.tsx": "16", "/Users/<USER>/Documents/idc-uniform/src/components/theme-provider.tsx": "17", "/Users/<USER>/Documents/idc-uniform/src/components/theme-toggle.tsx": "18", "/Users/<USER>/Documents/idc-uniform/src/components/ui/badge.tsx": "19", "/Users/<USER>/Documents/idc-uniform/src/components/ui/button.tsx": "20", "/Users/<USER>/Documents/idc-uniform/src/components/ui/card.tsx": "21", "/Users/<USER>/Documents/idc-uniform/src/components/ui/input.tsx": "22", "/Users/<USER>/Documents/idc-uniform/src/components/ui/label.tsx": "23", "/Users/<USER>/Documents/idc-uniform/src/components/ui/select.tsx": "24", "/Users/<USER>/Documents/idc-uniform/src/components/whatsapp-contact.tsx": "25", "/Users/<USER>/Documents/idc-uniform/src/lib/utils.ts": "26", "/Users/<USER>/Documents/idc-uniform/src/types/index.ts": "27"}, {"size": 8801, "mtime": 1754205891480, "results": "28", "hashOfConfig": "29"}, {"size": 8691, "mtime": 1754205856898, "results": "30", "hashOfConfig": "29"}, {"size": 1601, "mtime": 1754205252659, "results": "31", "hashOfConfig": "29"}, {"size": 362, "mtime": 1754206103962, "results": "32", "hashOfConfig": "29"}, {"size": 1156, "mtime": 1754206113311, "results": "33", "hashOfConfig": "29"}, {"size": 7596, "mtime": 1754206022475, "results": "34", "hashOfConfig": "29"}, {"size": 8351, "mtime": 1754206190069, "results": "35", "hashOfConfig": "29"}, {"size": 5766, "mtime": 1754205671653, "results": "36", "hashOfConfig": "29"}, {"size": 261, "mtime": 1754205965732, "results": "37", "hashOfConfig": "29"}, {"size": 11153, "mtime": 1754205935787, "results": "38", "hashOfConfig": "29"}, {"size": 1514, "mtime": 1754205958889, "results": "39", "hashOfConfig": "29"}, {"size": 2196, "mtime": 1754205628651, "results": "40", "hashOfConfig": "29"}, {"size": 6713, "mtime": 1754205550608, "results": "41", "hashOfConfig": "29"}, {"size": 4528, "mtime": 1754205718572, "results": "42", "hashOfConfig": "29"}, {"size": 474, "mtime": 1754205605680, "results": "43", "hashOfConfig": "29"}, {"size": 2899, "mtime": 1754205642971, "results": "44", "hashOfConfig": "29"}, {"size": 476, "mtime": 1754205194831, "results": "45", "hashOfConfig": "29"}, {"size": 1009, "mtime": 1754205203373, "results": "46", "hashOfConfig": "29"}, {"size": 1141, "mtime": 1754205467177, "results": "47", "hashOfConfig": "29"}, {"size": 1835, "mtime": 1754205215326, "results": "48", "hashOfConfig": "29"}, {"size": 1890, "mtime": 1754205235240, "results": "49", "hashOfConfig": "29"}, {"size": 824, "mtime": 1754205414814, "results": "50", "hashOfConfig": "29"}, {"size": 709, "mtime": 1754205422359, "results": "51", "hashOfConfig": "29"}, {"size": 5624, "mtime": 1754205446015, "results": "52", "hashOfConfig": "29"}, {"size": 5307, "mtime": 1754205521446, "results": "53", "hashOfConfig": "29"}, {"size": 1178, "mtime": 1754205097364, "results": "54", "hashOfConfig": "29"}, {"size": 1051, "mtime": 1754205106532, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1t2h2b1", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/idc-uniform/src/app/about/page.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/app/contact/page.tsx", ["137"], [], "/Users/<USER>/Documents/idc-uniform/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/app/loading.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/app/not-found.tsx", ["138", "139"], [], "/Users/<USER>/Documents/idc-uniform/src/app/page.tsx", ["140", "141"], [], "/Users/<USER>/Documents/idc-uniform/src/app/products/[id]/page.tsx", ["142"], [], "/Users/<USER>/Documents/idc-uniform/src/app/products/page.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/app/robots.ts", [], [], "/Users/<USER>/Documents/idc-uniform/src/app/services/page.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/app/sitemap.ts", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/category-card.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/footer.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/navigation.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/placeholder-image.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/product-card.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/theme-provider.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/theme-toggle.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/ui/card.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/ui/input.tsx", ["143"], [], "/Users/<USER>/Documents/idc-uniform/src/components/ui/label.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/ui/select.tsx", [], [], "/Users/<USER>/Documents/idc-uniform/src/components/whatsapp-contact.tsx", ["144"], [], "/Users/<USER>/Documents/idc-uniform/src/lib/utils.ts", ["145", "146"], [], "/Users/<USER>/Documents/idc-uniform/src/types/index.ts", [], [], {"ruleId": "147", "severity": 2, "message": "148", "line": 73, "column": 47, "nodeType": "149", "messageId": "150", "suggestions": "151"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 16, "column": 29, "nodeType": "149", "messageId": "150", "suggestions": "152"}, {"ruleId": "147", "severity": 2, "message": "148", "line": 16, "column": 49, "nodeType": "149", "messageId": "150", "suggestions": "153"}, {"ruleId": "154", "severity": 2, "message": "155", "line": 41, "column": 8, "nodeType": "156", "messageId": "157", "endLine": 41, "endColumn": 14}, {"ruleId": "147", "severity": 2, "message": "148", "line": 169, "column": 41, "nodeType": "149", "messageId": "150", "suggestions": "158"}, {"ruleId": "154", "severity": 2, "message": "155", "line": 63, "column": 8, "nodeType": "156", "messageId": "157", "endLine": 63, "endColumn": 14}, {"ruleId": "159", "severity": 2, "message": "160", "line": 4, "column": 18, "nodeType": "161", "messageId": "162", "endLine": 4, "endColumn": 28, "suggestions": "163"}, {"ruleId": "164", "severity": 1, "message": "165", "line": 9, "column": 37, "nodeType": null, "messageId": "166", "endLine": 9, "endColumn": 41}, {"ruleId": "167", "severity": 2, "message": "168", "line": 16, "column": 50, "nodeType": "169", "messageId": "170", "endLine": 16, "endColumn": 53, "suggestions": "171"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 16, "column": 67, "nodeType": "169", "messageId": "170", "endLine": 16, "endColumn": 70, "suggestions": "172"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["173", "174", "175", "176"], ["177", "178", "179", "180"], ["181", "182", "183", "184"], "react/jsx-no-undef", "'Script' is not defined.", "JSXIdentifier", "undefined", ["185", "186", "187", "188"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["189"], "@typescript-eslint/no-unused-vars", "'Size' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["190", "191"], ["192", "193"], {"messageId": "194", "data": "195", "fix": "196", "desc": "197"}, {"messageId": "194", "data": "198", "fix": "199", "desc": "200"}, {"messageId": "194", "data": "201", "fix": "202", "desc": "203"}, {"messageId": "194", "data": "204", "fix": "205", "desc": "206"}, {"messageId": "194", "data": "207", "fix": "208", "desc": "197"}, {"messageId": "194", "data": "209", "fix": "210", "desc": "200"}, {"messageId": "194", "data": "211", "fix": "212", "desc": "203"}, {"messageId": "194", "data": "213", "fix": "214", "desc": "206"}, {"messageId": "194", "data": "215", "fix": "216", "desc": "197"}, {"messageId": "194", "data": "217", "fix": "218", "desc": "200"}, {"messageId": "194", "data": "219", "fix": "220", "desc": "203"}, {"messageId": "194", "data": "221", "fix": "222", "desc": "206"}, {"messageId": "194", "data": "223", "fix": "224", "desc": "197"}, {"messageId": "194", "data": "225", "fix": "226", "desc": "200"}, {"messageId": "194", "data": "227", "fix": "228", "desc": "203"}, {"messageId": "194", "data": "229", "fix": "230", "desc": "206"}, {"messageId": "231", "fix": "232", "desc": "233"}, {"messageId": "234", "fix": "235", "desc": "236"}, {"messageId": "237", "fix": "238", "desc": "239"}, {"messageId": "234", "fix": "240", "desc": "236"}, {"messageId": "237", "fix": "241", "desc": "239"}, "replaceWithAlt", {"alt": "242"}, {"range": "243", "text": "244"}, "Replace with `&apos;`.", {"alt": "245"}, {"range": "246", "text": "247"}, "Replace with `&lsquo;`.", {"alt": "248"}, {"range": "249", "text": "250"}, "Replace with `&#39;`.", {"alt": "251"}, {"range": "252", "text": "253"}, "Replace with `&rsquo;`.", {"alt": "242"}, {"range": "254", "text": "255"}, {"alt": "245"}, {"range": "256", "text": "257"}, {"alt": "248"}, {"range": "258", "text": "259"}, {"alt": "251"}, {"range": "260", "text": "261"}, {"alt": "242"}, {"range": "262", "text": "263"}, {"alt": "245"}, {"range": "264", "text": "265"}, {"alt": "248"}, {"range": "266", "text": "267"}, {"alt": "251"}, {"range": "268", "text": "269"}, {"alt": "242"}, {"range": "270", "text": "271"}, {"alt": "245"}, {"range": "272", "text": "273"}, {"alt": "248"}, {"range": "274", "text": "275"}, {"alt": "251"}, {"range": "276", "text": "277"}, "replaceEmptyInterfaceWithSuper", {"range": "278", "text": "279"}, "Replace empty interface with a type alias.", "suggestUnknown", {"range": "280", "text": "281"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "282", "text": "283"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "284", "text": "281"}, {"range": "285", "text": "283"}, "&apos;", [2386, 2483], "\n                Fill out the form below and we&apos;ll get back to you within 24 hours\n              ", "&lsquo;", [2386, 2483], "\n                Fill out the form below and we&lsquo;ll get back to you within 24 hours\n              ", "&#39;", [2386, 2483], "\n                Fill out the form below and we&#39;ll get back to you within 24 hours\n              ", "&rsquo;", [2386, 2483], "\n                Fill out the form below and we&rsquo;ll get back to you within 24 hours\n              ", [590, 744], "\n            Sorry, we couldn&apos;t find the page you're looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn&lsquo;t find the page you're looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn&#39;t find the page you're looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn&rsquo;t find the page you're looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn't find the page you&apos;re looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn't find the page you&lsquo;re looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn't find the page you&#39;re looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [590, 744], "\n            Sorry, we couldn't find the page you&rsquo;re looking for. \n            It might have been moved, deleted, or you entered the wrong URL.\n          ", [6853, 6929], "\n              Ready to Upgrade Your Team&apos;s Professional Image?\n            ", [6853, 6929], "\n              Ready to Upgrade Your Team&lsquo;s Professional Image?\n            ", [6853, 6929], "\n              Ready to Upgrade Your Team&#39;s Professional Image?\n            ", [6853, 6929], "\n              Ready to Upgrade Your Team&rsquo;s Professional Image?\n            ", [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [462, 465], "unknown", [462, 465], "never", [479, 482], [479, 482]]