"use strict";(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{a.exports=require("util")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{a.exports=require("path")},36440:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},41025:a=>{a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41382:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},51467:(a,b,c)=>{c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,52655)),"/Users/<USER>/Documents/idc-uniform/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/idc-uniform/src/app/layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"/Users/<USER>/Documents/idc-uniform/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"/Users/<USER>/Documents/idc-uniform/src/app/not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/idc-uniform/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},52655:(a,b,c)=>{c.r(b),c.d(b,{default:()=>r});var d=c(37413),e=c(23469),f=c(78963),g=c(36440),h=c(4536),i=c.n(h);function j({category:a}){return(0,d.jsxs)(f.Zp,{className:"group overflow-hidden transition-all duration-300 hover:shadow-lg",children:[(0,d.jsx)(f.aR,{className:"p-0",children:(0,d.jsxs)("div",{className:"relative aspect-[4/3] overflow-hidden bg-muted flex items-center justify-center",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),(0,d.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDC54"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:a.name})]}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 text-white",children:[(0,d.jsx)("h3",{className:"text-xl font-bold mb-1",children:a.name}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:a.description})]})]})}),(0,d.jsx)(f.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.subcategories.slice(0,3).map(a=>(0,d.jsx)("span",{className:"text-xs bg-muted px-2 py-1 rounded-md text-muted-foreground",children:a},a)),a.subcategories.length>3&&(0,d.jsxs)("span",{className:"text-xs bg-muted px-2 py-1 rounded-md text-muted-foreground",children:["+",a.subcategories.length-3," more"]})]}),(0,d.jsx)(e.$,{asChild:!0,className:"w-full group",children:(0,d.jsxs)(i(),{href:`/products?category=${a.id}`,children:["Browse ",a.name,(0,d.jsx)(g.A,{className:"w-4 h-4 ml-2 transition-transform group-hover:translate-x-1"})]})})]})})]})}var k=c(89573),l=c(64544),m=c(61658),n=c(69117),o=c(53148),p=c(61227),q=c(41382);function r(){let a={"@context":"https://schema.org","@type":"Organization",name:"IDC Uniform",description:"Professional uniforms for hospitals, hotels, security, industrial, and custom applications",url:"https://idcuniform.com",logo:"https://idcuniform.com/logo.png",contactPoint:{"@type":"ContactPoint",telephone:companyData.company.whatsapp,contactType:"customer service",availableLanguage:"English"},address:{"@type":"PostalAddress",streetAddress:companyData.locations[0].address,addressLocality:"City",addressRegion:"State",postalCode:"12345",addressCountry:"US"},sameAs:[companyData.company.socialMedia.facebook,companyData.company.socialMedia.instagram,companyData.company.socialMedia.linkedin]};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(Script,{id:"structured-data",type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a)}}),(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)(l.Navigation,{}),(0,d.jsx)("section",{className:"relative py-20 lg:py-32",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,d.jsxs)("h1",{className:"text-4xl lg:text-6xl font-bold tracking-tight mb-6",children:["Professional Uniforms for"," ",(0,d.jsx)("span",{className:"text-primary",children:"Every Industry"})]}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto",children:"IDC Uniform specializes in high-quality uniforms for hospitals, hotels, security, industrial, and custom applications. Professional attire solutions for your business needs."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(e.$,{size:"lg",className:"text-lg px-8",asChild:!0,children:(0,d.jsxs)(i(),{href:"/products",children:["Browse Products",(0,d.jsx)(g.A,{className:"ml-2 h-5 w-5"})]})}),(0,d.jsx)(e.$,{variant:"outline",size:"lg",className:"text-lg px-8",asChild:!0,children:(0,d.jsx)(i(),{href:"/contact",children:"Get Custom Quote"})})]})]})})}),(0,d.jsx)("section",{className:"py-20 bg-muted/50",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold mb-4",children:"Why Choose IDC Uniform?"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"We provide professional uniform solutions with unmatched quality and service"})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)(f.Zp,{className:"text-center",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,d.jsx)(n.A,{className:"h-6 w-6 text-primary"})}),(0,d.jsx)(f.ZB,{children:"Premium Quality"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(f.BT,{children:"High-quality materials and construction ensure durability and professional appearance"})})]}),(0,d.jsxs)(f.Zp,{className:"text-center",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,d.jsx)(o.A,{className:"h-6 w-6 text-primary"})}),(0,d.jsx)(f.ZB,{children:"Fast Delivery"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(f.BT,{children:"Quick turnaround times and reliable delivery to meet your business needs"})})]}),(0,d.jsxs)(f.Zp,{className:"text-center",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,d.jsx)(p.A,{className:"h-6 w-6 text-primary"})}),(0,d.jsx)(f.ZB,{children:"Custom Solutions"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(f.BT,{children:"Tailored uniform solutions designed specifically for your industry requirements"})})]}),(0,d.jsxs)(f.Zp,{className:"text-center",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)("div",{className:"mx-auto mb-4 h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,d.jsx)(q.A,{className:"h-6 w-6 text-primary"})}),(0,d.jsx)(f.ZB,{children:"Expert Support"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)(f.BT,{children:"Dedicated customer service team to help you find the perfect uniform solution"})})]})]})]})}),(0,d.jsx)("section",{className:"py-20",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold mb-4",children:"Our Uniform Categories"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Explore our comprehensive range of professional uniforms designed for every industry"})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:m.L.map(a=>(0,d.jsx)(j,{category:a},a.id))})]})}),(0,d.jsx)("section",{className:"py-20 bg-muted/50",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,d.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold mb-6",children:"Ready to Upgrade Your Team's Professional Image?"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground mb-8",children:"Contact us today for a free consultation and custom quote"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(e.$,{size:"lg",className:"text-lg px-8",asChild:!0,children:(0,d.jsx)(i(),{href:"/contact",children:"Contact Us Now"})}),(0,d.jsx)(e.$,{variant:"outline",size:"lg",className:"text-lg px-8",asChild:!0,children:(0,d.jsx)(i(),{href:"/products",children:"View Catalog"})})]})]})})}),(0,d.jsx)(k.w,{})]})]})}},53148:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},61227:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},61658:a=>{a.exports=JSON.parse('{"L":[{"id":"hospital","name":"Hospital Uniforms","description":"Professional medical attire for healthcare professionals","image":"/images/categories/hospital.jpg","subcategories":["Scrubs","Lab Coats","Nurse Uniforms","Doctor Coats"]},{"id":"hotel","name":"Hotel Uniforms","description":"Elegant hospitality uniforms for hotels and restaurants","image":"/images/categories/hotel.jpg","subcategories":["Front Desk","Housekeeping","Restaurant","Concierge"]},{"id":"security","name":"Security Uniforms","description":"Professional security and guard uniforms","image":"/images/categories/security.jpg","subcategories":["Security Guards","Event Security","Corporate Security","Patrol Uniforms"]},{"id":"industrial","name":"Industrial Uniforms","description":"Durable workwear for industrial and manufacturing environments","image":"/images/categories/industrial.jpg","subcategories":["Safety Wear","Coveralls","Work Shirts","High-Vis Clothing"]},{"id":"custom","name":"Custom Uniforms","description":"Tailored uniform solutions for your specific needs","image":"/images/categories/custom.jpg","subcategories":["Corporate Wear","School Uniforms","Sports Teams","Special Events"]}]}')},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69117:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78963:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(37413),e=c(61120),f=c(10974);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,743,386],()=>b(b.s=51467));module.exports=c})();