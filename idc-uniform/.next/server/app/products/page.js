(()=>{var a={};a.id=571,a.ids=[571],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5364:(a,b,c)=>{Promise.resolve().then(c.bind(c,8547))},7572:(a,b,c)=>{Promise.resolve().then(c.bind(c,48261))},8547:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/idc-uniform/src/app/products/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/idc-uniform/src/app/products/page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(16725),g=c(78272),h=c(3589),i=c(13964),j=c(4780);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-border bg-background text-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48261:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z});var d=c(60687),e=c(43210),f=c(15926),g=c(69432),h=c(24224),i=c(4780);let j=(0,h.F)("inline-flex items-center rounded-full border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function k({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,i.cn)(j({variant:b}),a),...c})}var l=c(29523),m=c(44493),n=c(62688);let o=(0,n.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),p=(0,n.A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var q=c(85814),r=c.n(q);function s({product:a}){return(0,d.jsxs)(m.Zp,{className:"group overflow-hidden transition-all duration-300 hover:shadow-lg",children:[(0,d.jsx)(m.aR,{className:"p-0",children:(0,d.jsxs)("div",{className:"relative aspect-square overflow-hidden bg-muted flex items-center justify-center",children:[(0,d.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,d.jsx)("div",{className:"text-6xl mb-2",children:"\uD83D\uDC55"}),(0,d.jsx)("div",{className:"text-sm font-medium",children:"Product Image"})]}),!a.inStock&&(0,d.jsx)("div",{className:"absolute inset-0 bg-black/50 flex items-center justify-center",children:(0,d.jsx)(k,{variant:"destructive",children:"Out of Stock"})}),(0,d.jsx)("div",{className:"absolute top-2 right-2",children:(0,d.jsx)(k,{variant:"secondary",children:a.category})})]})}),(0,d.jsx)(m.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 group-hover:text-primary transition-colors",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:a.description}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-2xl font-bold text-primary",children:(0,i.$g)(a.price)}),(0,d.jsxs)("div",{className:"flex gap-1",children:[a.sizes.slice(0,3).map(a=>(0,d.jsx)(k,{variant:"outline",className:"text-xs",children:a},a)),a.sizes.length>3&&(0,d.jsxs)(k,{variant:"outline",className:"text-xs",children:["+",a.sizes.length-3]})]})]})]})}),(0,d.jsxs)(m.wL,{className:"p-4 pt-0 flex gap-2",children:[(0,d.jsx)(l.$,{asChild:!0,variant:"outline",size:"sm",className:"flex-1",children:(0,d.jsxs)(r(),{href:`/products/${a.id}`,children:[(0,d.jsx)(o,{className:"w-4 h-4 mr-2"}),"View Details"]})}),(0,d.jsxs)(l.$,{size:"sm",className:"flex-1",disabled:!a.inStock,children:[(0,d.jsx)(p,{className:"w-4 h-4 mr-2"}),"Quick Order"]})]})]})}var t=c(89667),u=c(15079);let v=(0,n.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),w=(0,n.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),x=JSON.parse('{"Z":[{"id":"HOSP001","name":"Premium Medical Scrubs Set","category":"hospital","price":"45.99","sizes":["XS","S","M","L","XL","XXL"],"colors":["White","Navy","Black","Teal","Royal Blue"],"images":["/images/products/scrubs-1.jpg","/images/products/scrubs-2.jpg","/images/products/scrubs-3.jpg"],"description":"High-quality medical scrubs made from antimicrobial fabric. Perfect for healthcare professionals who demand comfort and durability.","features":["Antimicrobial fabric treatment","Multiple utility pockets","Easy care - machine washable","Fade-resistant colors","Comfortable stretch fabric"],"inStock":true},{"id":"HOSP002","name":"Professional Lab Coat","category":"hospital","price":"35.99","sizes":["S","M","L","XL","XXL","XXXL"],"colors":["White","Light Blue"],"images":["/images/products/labcoat-1.jpg","/images/products/labcoat-2.jpg"],"description":"Classic white lab coat with professional styling and practical features for medical professionals.","features":["100% cotton blend","Three front pockets","Adjustable cuffs","Professional appearance","Stain-resistant treatment"],"inStock":true},{"id":"HOTEL001","name":"Front Desk Uniform Set","category":"hotel","price":"65.99","sizes":["XS","S","M","L","XL","XXL"],"colors":["Black","Navy","Charcoal"],"images":["/images/products/frontdesk-1.jpg","/images/products/frontdesk-2.jpg"],"description":"Elegant front desk uniform combining professionalism with comfort for hospitality staff.","features":["Wrinkle-resistant fabric","Professional tailoring","Comfortable fit","Easy maintenance","Modern styling"],"inStock":true},{"id":"SEC001","name":"Security Guard Uniform","category":"security","price":"55.99","sizes":["S","M","L","XL","XXL","XXXL"],"colors":["Black","Navy","Dark Gray"],"images":["/images/products/security-1.jpg","/images/products/security-2.jpg"],"description":"Professional security uniform designed for durability and authority.","features":["Reinforced stitching","Multiple cargo pockets","Badge-ready design","Durable fabric","Professional appearance"],"inStock":true},{"id":"IND001","name":"Industrial Coveralls","category":"industrial","price":"75.99","sizes":["M","L","XL","XXL","XXXL"],"colors":["Navy","Gray","Orange"],"images":["/images/products/coveralls-1.jpg","/images/products/coveralls-2.jpg"],"description":"Heavy-duty coveralls for industrial work environments with safety features.","features":["Flame-resistant fabric","Reinforced knees","Multiple tool pockets","Safety compliance","Durable construction"],"inStock":true},{"id":"CUST001","name":"Custom Corporate Polo","category":"custom","price":"29.99","sizes":["XS","S","M","L","XL","XXL","XXXL"],"colors":["White","Black","Navy","Red","Green","Blue"],"images":["/images/products/polo-1.jpg","/images/products/polo-2.jpg"],"description":"Customizable polo shirts perfect for corporate uniforms and team wear.","features":["100% cotton pique","Custom embroidery available","Colorfast dyes","Professional collar","Comfortable fit"],"inStock":true}]}'),y=JSON.parse('{"L":[{"id":"hospital","name":"Hospital Uniforms","description":"Professional medical attire for healthcare professionals","image":"/images/categories/hospital.jpg","subcategories":["Scrubs","Lab Coats","Nurse Uniforms","Doctor Coats"]},{"id":"hotel","name":"Hotel Uniforms","description":"Elegant hospitality uniforms for hotels and restaurants","image":"/images/categories/hotel.jpg","subcategories":["Front Desk","Housekeeping","Restaurant","Concierge"]},{"id":"security","name":"Security Uniforms","description":"Professional security and guard uniforms","image":"/images/categories/security.jpg","subcategories":["Security Guards","Event Security","Corporate Security","Patrol Uniforms"]},{"id":"industrial","name":"Industrial Uniforms","description":"Durable workwear for industrial and manufacturing environments","image":"/images/categories/industrial.jpg","subcategories":["Safety Wear","Coveralls","Work Shirts","High-Vis Clothing"]},{"id":"custom","name":"Custom Uniforms","description":"Tailored uniform solutions for your specific needs","image":"/images/categories/custom.jpg","subcategories":["Corporate Wear","School Uniforms","Sports Teams","Special Events"]}]}');function z(){let[a,b]=(0,e.useState)(""),[c,h]=(0,e.useState)("all"),[i,j]=(0,e.useState)("name"),m=(0,e.useMemo)(()=>{let b=x.Z;return a&&(b=b.filter(b=>b.name.toLowerCase().includes(a.toLowerCase())||b.description.toLowerCase().includes(a.toLowerCase()))),"all"!==c&&(b=b.filter(a=>a.category===c)),b.sort((a,b)=>{switch(i){case"price-low":return parseFloat(a.price)-parseFloat(b.price);case"price-high":return parseFloat(b.price)-parseFloat(a.price);default:return a.name.localeCompare(b.name)}}),b},[a,c,i]);return(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)(f.Navigation,{}),(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h1",{className:"text-4xl lg:text-5xl font-bold mb-4",children:"Our Products"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Discover our comprehensive range of professional uniforms designed for every industry"})]}),(0,d.jsx)("div",{className:"bg-muted/50 rounded-lg p-6 mb-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,d.jsx)(t.p,{placeholder:"Search products...",value:a,onChange:a=>b(a.target.value),className:"pl-10"})]}),(0,d.jsxs)(u.l6,{value:c,onValueChange:h,children:[(0,d.jsx)(u.bq,{children:(0,d.jsx)(u.yv,{placeholder:"All Categories"})}),(0,d.jsxs)(u.gC,{children:[(0,d.jsx)(u.eb,{value:"all",children:"All Categories"}),y.L.map(a=>(0,d.jsx)(u.eb,{value:a.id,children:a.name},a.id))]})]}),(0,d.jsxs)(u.l6,{value:i,onValueChange:j,children:[(0,d.jsx)(u.bq,{children:(0,d.jsx)(u.yv,{placeholder:"Sort by"})}),(0,d.jsxs)(u.gC,{children:[(0,d.jsx)(u.eb,{value:"name",children:"Name (A-Z)"}),(0,d.jsx)(u.eb,{value:"price-low",children:"Price (Low to High)"}),(0,d.jsx)(u.eb,{value:"price-high",children:"Price (High to Low)"})]})]}),(0,d.jsxs)(l.$,{variant:"outline",onClick:()=>{b(""),h("all"),j("name")},children:[(0,d.jsx)(w,{className:"w-4 h-4 mr-2"}),"Clear Filters"]})]})}),(0,d.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"text-muted-foreground",children:["Showing ",m.length," of ",x.Z.length," products"]}),"all"!==c&&(0,d.jsx)(k,{variant:"secondary",children:y.L.find(a=>a.id===c)?.name})]})}),m.length>0?(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:m.map(a=>(0,d.jsx)(s,{product:a},a.id))}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"No products found"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Try adjusting your search criteria or browse all categories"}),(0,d.jsx)(l.$,{onClick:()=>{b(""),h("all"),j("name")},children:"View All Products"})]})]}),(0,d.jsx)(g.w,{})]})}},59459:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,8547)),"/Users/<USER>/Documents/idc-uniform/src/app/products/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/idc-uniform/src/app/layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"/Users/<USER>/Documents/idc-uniform/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"/Users/<USER>/Documents/idc-uniform/src/app/not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/idc-uniform/src/app/products/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/products/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,743,425,386,24],()=>b(b.s=59459));module.exports=c})();