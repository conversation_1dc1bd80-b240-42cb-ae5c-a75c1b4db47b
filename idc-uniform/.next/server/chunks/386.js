exports.id=386,exports.ids=[386],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{$g:()=>g,L4:()=>h,cn:()=>f,zp:()=>i});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){let b="string"==typeof a?parseFloat(a):a;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(b)}function h(a,b){return`Hello IDC Uniform! 👋

I'm interested in:
📦 Product: ${a.name}
🏷️ Product Code: ${a.id}
📏 Size: ${b.size}
🔢 Quantity: ${b.quantity}
🎨 Color: ${b.color||"Standard"}

Customer Details:
👤 Name: ${b.customerName}
📱 Phone: ${b.customerPhone}
🏢 Company: ${b.companyName||"Individual"}

Additional Notes:
${b.notes||"None"}

Please provide pricing and availability.`}function i(a,b){let c=encodeURIComponent(a);return`https://wa.me/${b}?text=${c}`}},10974:(a,b,c)=>{"use strict";c.d(b,{$g:()=>g,cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){let b="string"==typeof a?parseFloat(a):a;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(b)}},15926:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>r});var d=c(60687),e=c(43210),f=c(21134),g=c(363),h=c(10218),i=c(29523);function j(){let{theme:a,setTheme:b}=(0,h.D)(),[c,j]=e.useState(!1);return(e.useEffect(()=>{j(!0)},[]),c)?(0,d.jsxs)(i.$,{variant:"ghost",size:"icon",onClick:()=>b("light"===a?"dark":"light"),className:"w-9 h-9",children:[(0,d.jsx)(f.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,d.jsx)(g.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,d.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}):(0,d.jsxs)(i.$,{variant:"ghost",size:"icon",className:"w-9 h-9",children:[(0,d.jsx)(f.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}var k=c(48340),l=c(41550),m=c(11860),n=c(12941),o=c(85814),p=c.n(o);let q=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Contact",href:"/contact"}];function r(){let[a,b]=(0,e.useState)(!1);return(0,d.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsxs)("div",{className:"hidden md:flex items-center justify-between py-2 text-sm border-b border-border",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(k.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:"+1234567890"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(l.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:"<EMAIL>"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"Mon-Fri 9AM-6PM"}),(0,d.jsx)(j,{})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(p(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-primary rounded-md flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"I"})}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-bold text-lg leading-none",children:"IDC Uniform"}),(0,d.jsx)("span",{className:"text-xs text-muted-foreground",children:"Professional Uniforms"})]})]})}),(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:q.map(a=>(0,d.jsx)(p(),{href:a.href,className:"text-sm font-medium transition-colors hover:text-primary",children:a.name},a.name))}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,d.jsx)(p(),{href:"/contact",children:"Get Quote"})}),(0,d.jsx)(i.$,{size:"sm",asChild:!0,children:(0,d.jsx)(p(),{href:"/contact",children:"Contact Us"})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 md:hidden",children:[(0,d.jsx)(j,{}),(0,d.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>b(!a),children:a?(0,d.jsx)(m.A,{className:"h-5 w-5"}):(0,d.jsx)(n.A,{className:"h-5 w-5"})})]})]}),a&&(0,d.jsx)("div",{className:"md:hidden border-t border-border",children:(0,d.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[q.map(a=>(0,d.jsx)(p(),{href:a.href,className:"block px-3 py-2 text-base font-medium transition-colors hover:text-primary hover:bg-accent rounded-md",onClick:()=>b(!1),children:a.name},a.name)),(0,d.jsxs)("div",{className:"flex flex-col space-y-2 px-3 pt-4",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full",asChild:!0,children:(0,d.jsx)(p(),{href:"/contact",children:"Get Quote"})}),(0,d.jsx)(i.$,{size:"sm",className:"w-full",asChild:!0,children:(0,d.jsx)(p(),{href:"/contact",children:"Contact Us"})})]})]})})]})})}},23469:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(37413),e=c(61120),f=c(70403),g=c(50662),h=c(10974);let i=(0,g.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(4780);let i=(0,g.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},36204:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,15926))},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(37413),e=c(4536),f=c.n(e),g=c(23469),h=c(64544),i=c(89573);function j(){return(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)(h.Navigation,{}),(0,d.jsx)("div",{className:"container mx-auto px-4 py-20",children:(0,d.jsxs)("div",{className:"text-center max-w-2xl mx-auto",children:[(0,d.jsx)("div",{className:"text-8xl mb-8",children:"\uD83D\uDD0D"}),(0,d.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Page Not Found"}),(0,d.jsx)("p",{className:"text-xl text-muted-foreground mb-8",children:"Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(g.$,{asChild:!0,size:"lg",children:(0,d.jsx)(f(),{href:"/",children:"Go Home"})}),(0,d.jsx)(g.$,{variant:"outline",size:"lg",asChild:!0,children:(0,d.jsx)(f(),{href:"/products",children:"Browse Products"})})]})]})}),(0,d.jsx)(i.w,{})]})}},57984:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},61135:()=>{},64544:(a,b,c)=>{"use strict";c.d(b,{Navigation:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/idc-uniform/src/components/navigation.tsx","Navigation")},67393:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(37413);function e(){return(0,d.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})})}},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},71136:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},72652:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,64544))},78335:()=>{},83701:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/idc-uniform/src/components/theme-provider.tsx","ThemeProvider")},85176:(a,b,c)=>{Promise.resolve().then(c.bind(c,83701))},89573:(a,b,c)=>{"use strict";c.d(b,{w:()=>r});var d=c(37413),e=c(4536),f=c.n(e),g=c(23469),h=c(61120),i=c(10974);let j=h.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,i.cn)("flex h-10 w-full rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));j.displayName="Input";var k=c(71750),l=c(60343),m=c(49046),n=c(45868),o=c(56378),p=c(96262);let q={products:[{name:"Hospital Uniforms",href:"/products?category=hospital"},{name:"Hotel Uniforms",href:"/products?category=hotel"},{name:"Security Uniforms",href:"/products?category=security"},{name:"Industrial Uniforms",href:"/products?category=industrial"},{name:"Custom Uniforms",href:"/products?category=custom"}],company:[{name:"About Us",href:"/about"},{name:"Our Services",href:"/services"},{name:"Quality Assurance",href:"/quality"},{name:"Careers",href:"/careers"},{name:"Contact",href:"/contact"}],support:[{name:"Size Guide",href:"/size-guide"},{name:"Care Instructions",href:"/care-instructions"},{name:"Shipping Info",href:"/shipping"},{name:"Returns",href:"/returns"},{name:"FAQ",href:"/faq"}],legal:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"Refund Policy",href:"/refunds"}]};function r(){return(0,d.jsx)("footer",{className:"bg-muted/50 border-t border-border",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-primary rounded-md flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"I"})}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-bold text-lg leading-none",children:"IDC Uniform"}),(0,d.jsx)("span",{className:"text-xs text-muted-foreground",children:"Professional Uniforms"})]})]}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4 max-w-md",children:"Professional uniform solutions for hospitals, hotels, security, industrial, and custom applications. Quality you can trust, service you can count on."}),(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("span",{children:"+1234567890"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("span",{children:"<EMAIL>"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("span",{children:"123 Business St, City, State 12345"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,d.jsx)(n.A,{className:"h-4 w-4"})}),(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,d.jsx)(o.A,{className:"h-4 w-4"})}),(0,d.jsx)(g.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,d.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",children:"Products"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.products.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",children:"Company"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.company.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold mb-4",children:"Support"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.support.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:a.name})},a.name))})]})]}),(0,d.jsx)("div",{className:"border-t border-border mt-8 pt-8",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",children:"Stay Updated"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Subscribe to our newsletter for the latest uniform trends and offers."})]}),(0,d.jsxs)("div",{className:"flex w-full md:w-auto gap-2",children:[(0,d.jsx)(j,{placeholder:"Enter your email",className:"md:w-64"}),(0,d.jsx)(g.$,{children:"Subscribe"})]})]})}),(0,d.jsxs)("div",{className:"border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 IDC Uniform. All rights reserved."}),(0,d.jsx)("div",{className:"flex space-x-4",children:q.legal.map(a=>(0,d.jsx)(f(),{href:a.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:a.name},a.name))})]})]})})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,metadata:()=>h});var d=c(37413),e=c(83701),f=c(4890),g=c.n(f);c(61135);let h={title:"IDC Uniform - Professional Uniforms for Every Industry",description:"IDC Uniform specializes in high-quality uniforms for hospitals, hotels, security, industrial, and custom applications. Professional attire solutions for your business needs.",keywords:["uniforms","medical scrubs","hotel uniforms","security uniforms","industrial workwear","custom uniforms"],authors:[{name:"IDC Uniform"}],creator:"IDC Uniform",publisher:"IDC Uniform",openGraph:{title:"IDC Uniform - Professional Uniforms for Every Industry",description:"Professional uniforms for hospitals, hotels, security, industrial, and custom applications.",url:"https://idcuniform.com",siteName:"IDC Uniform",type:"website"},twitter:{card:"summary_large_image",title:"IDC Uniform - Professional Uniforms for Every Industry",description:"Professional uniforms for hospitals, hotels, security, industrial, and custom applications."}};function i({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:`${g().variable} font-sans antialiased`,children:(0,d.jsx)(e.ThemeProvider,{children:a})})})}},94904:(a,b,c)=>{Promise.resolve().then(c.bind(c,96871))},96487:()=>{},96871:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>f});var d=c(60687);c(43210);var e=c(10218);function f({children:a,...b}){return(0,d.jsx)(e.N,{attribute:"data-theme",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!1,...b,children:a})}}};