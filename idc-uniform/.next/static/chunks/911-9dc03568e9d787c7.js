"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[911],{1362:(e,r,t)=>{t.d(r,{D:()=>c,N:()=>d});var o=t(2115),n=(e,r,t,o,n,a,l,s)=>{let i=document.documentElement,c=["light","dark"];function d(r){var t;(Array.isArray(e)?e:[e]).forEach(e=>{let t="class"===e,o=t&&a?n.map(e=>a[e]||e):n;t?(i.classList.remove(...o),i.classList.add(a&&a[r]?a[r]:r)):i.setAttribute(e,r)}),t=r,s&&c.includes(t)&&(i.style.colorScheme=t)}if(o)d(o);else try{let e=localStorage.getItem(r)||t,o=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(o)}catch(e){}},a=["light","dark"],l="(prefers-color-scheme: dark)",s=o.createContext(void 0),i={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=o.useContext(s))?e:i},d=e=>o.useContext(s)?o.createElement(o.Fragment,null,e.children):o.createElement(m,{...e}),u=["light","dark"],m=e=>{let{forcedTheme:r,disableTransitionOnChange:t=!1,enableSystem:n=!0,enableColorScheme:i=!0,storageKey:c="theme",themes:d=u,defaultTheme:m=n?"system":"light",attribute:g="data-theme",value:y,children:k,nonce:v,scriptProps:w}=e,[x,j]=o.useState(()=>f(c,m)),[z,E]=o.useState(()=>"system"===x?h():x),C=y?Object.values(y):d,P=o.useCallback(e=>{let r=e;if(!r)return;"system"===e&&n&&(r=h());let o=y?y[r]:r,l=t?b(v):null,s=document.documentElement,c=e=>{"class"===e?(s.classList.remove(...C),o&&s.classList.add(o)):e.startsWith("data-")&&(o?s.setAttribute(e,o):s.removeAttribute(e))};if(Array.isArray(g)?g.forEach(c):c(g),i){let e=a.includes(m)?m:null,t=a.includes(r)?r:e;s.style.colorScheme=t}null==l||l()},[v]),A=o.useCallback(e=>{let r="function"==typeof e?e(x):e;j(r);try{localStorage.setItem(c,r)}catch(e){}},[x]),S=o.useCallback(e=>{E(h(e)),"system"===x&&n&&!r&&P("system")},[x,r]);o.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(S),S(e),()=>e.removeListener(S)},[S]),o.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?j(e.newValue):A(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),o.useEffect(()=>{P(null!=r?r:x)},[r,x]);let M=o.useMemo(()=>({theme:x,setTheme:A,forcedTheme:r,resolvedTheme:"system"===x?z:x,themes:n?[...d,"system"]:d,systemTheme:n?z:void 0}),[x,A,r,z,n,d]);return o.createElement(s.Provider,{value:M},o.createElement(p,{forcedTheme:r,storageKey:c,attribute:g,enableSystem:n,enableColorScheme:i,defaultTheme:m,value:y,themes:d,nonce:v,scriptProps:w}),k)},p=o.memo(e=>{let{forcedTheme:r,storageKey:t,attribute:a,enableSystem:l,enableColorScheme:s,defaultTheme:i,value:c,themes:d,nonce:u,scriptProps:m}=e,p=JSON.stringify([a,t,i,r,d,c,l,s]).slice(1,-1);return o.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(p,")")}})}),f=(e,r)=>{let t;try{t=localStorage.getItem(e)||void 0}catch(e){}return t||r},b=e=>{let r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},h=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},2085:(e,r,t)=>{t.d(r,{F:()=>l});var o=t(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=o.$,l=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:s}=r,i=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],o=null==s?void 0:s[e];if(null===r)return null;let a=n(r)||n(o);return l[e][a]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return a(e,i,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...s,...c}[r]):({...s,...c})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2098:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2596:(e,r,t)=>{t.d(r,{$:()=>o});function o(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}},2664:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let o=t(9991),n=t(7102);function a(e){if(!(0,o.isAbsoluteUrl)(e))return!0;try{let r=(0,o.getLocationOrigin)(),t=new URL(e,r);return t.origin===r&&(0,n.hasBasePath)(t.pathname)}catch(e){return!1}}},2757:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return l}});let o=t(6966)._(t(8859)),n=/https?|ftp|gopher|file/;function a(e){let{auth:r,hostname:t}=e,a=e.protocol||"",l=e.pathname||"",s=e.hash||"",i=e.query||"",c=!1;r=r?encodeURIComponent(r).replace(/%3A/i,":")+"@":"",e.host?c=r+e.host:t&&(c=r+(~t.indexOf(":")?"["+t+"]":t),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(o.urlQueryToSearchParams(i)));let d=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||n.test(a))&&!1!==c?(c="//"+(c||""),l&&"/"!==l[0]&&(l="/"+l)):c||(c=""),s&&"#"!==s[0]&&(s="#"+s),d&&"?"!==d[0]&&(d="?"+d),""+a+c+(l=l.replace(/[?#]/g,encodeURIComponent))+(d=d.replace("#","%23"))+s}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},3180:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}},3509:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]])},4416:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},6101:(e,r,t)=>{t.d(r,{s:()=>l,t:()=>a});var o=t(2115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function l(...e){return o.useCallback(a(...e),e)}},6654:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return n}});let o=t(2115);function n(e,r){let t=(0,o.useRef)(null),n=(0,o.useRef)(null);return(0,o.useCallback)(o=>{if(null===o){let e=t.current;e&&(t.current=null,e());let r=n.current;r&&(n.current=null,r())}else e&&(t.current=a(e,o)),r&&(n.current=a(r,o))},[e,r])}function a(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},6874:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return h},useLinkStatus:function(){return y}});let o=t(6966),n=t(5155),a=o._(t(2115)),l=t(2757),s=t(5227),i=t(9818),c=t(6654),d=t(9991),u=t(5929);t(3230);let m=t(4930),p=t(2664),f=t(6634);function b(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function h(e){let r,t,o,[l,h]=(0,a.useOptimistic)(m.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:k,as:v,children:w,prefetch:x=null,passHref:j,replace:z,shallow:E,scroll:C,onClick:P,onMouseEnter:A,onTouchStart:S,legacyBehavior:M=!1,onNavigate:N,ref:O,unstable_dynamicOnHover:_,...T}=e;r=w,M&&("string"==typeof r||"number"==typeof r)&&(r=(0,n.jsx)("a",{children:r}));let L=a.default.useContext(s.AppRouterContext),I=!1!==x,R=null===x||"auto"===x?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:U,as:W}=a.default.useMemo(()=>{let e=b(k);return{href:e,as:v?b(v):e}},[k,v]);M&&(t=a.default.Children.only(r));let $=M?t&&"object"==typeof t&&t.ref:O,D=a.default.useCallback(e=>(null!==L&&(y.current=(0,m.mountLinkInstance)(e,U,L,R,I,h)),()=>{y.current&&((0,m.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,m.unmountPrefetchableInstance)(e)}),[I,U,L,R,h]),F={ref:(0,c.useMergedRef)(D,$),onClick(e){M||"function"!=typeof P||P(e),M&&t.props&&"function"==typeof t.props.onClick&&t.props.onClick(e),L&&(e.defaultPrevented||function(e,r,t,o,n,l,s){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let r=e.currentTarget.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(r)){n&&(e.preventDefault(),location.replace(r));return}if(e.preventDefault(),s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,f.dispatchNavigateAction)(t||r,n?"replace":"push",null==l||l,o.current)})}}(e,U,W,y,z,C,N))},onMouseEnter(e){M||"function"!=typeof A||A(e),M&&t.props&&"function"==typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),L&&I&&(0,m.onNavigationIntent)(e.currentTarget,!0===_)},onTouchStart:function(e){M||"function"!=typeof S||S(e),M&&t.props&&"function"==typeof t.props.onTouchStart&&t.props.onTouchStart(e),L&&I&&(0,m.onNavigationIntent)(e.currentTarget,!0===_)}};return(0,d.isAbsoluteUrl)(W)?F.href=W:M&&!j&&("a"!==t.type||"href"in t.props)||(F.href=(0,u.addBasePath)(W)),o=M?a.default.cloneElement(t,F):(0,n.jsx)("a",{...T,...F,children:r}),(0,n.jsx)(g.Provider,{value:l,children:o})}t(3180);let g=(0,a.createContext)(m.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(g);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8859:(e,r)=>{function t(e){let r={};for(let[t,o]of e.entries()){let e=r[t];void 0===e?r[t]=o:Array.isArray(e)?e.push(o):r[t]=[e,o]}return r}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let r=new URLSearchParams;for(let[t,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)r.append(t,o(e));else r.set(t,o(n));return r}function a(e){for(var r=arguments.length,t=Array(r>1?r-1:0),o=1;o<r;o++)t[o-1]=arguments[o];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,o]of r.entries())e.append(t,o)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{assign:function(){return a},searchParamsToUrlQuery:function(){return t},urlQueryToSearchParams:function(){return n}})},8883:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9688:(e,r,t)=>{t.d(r,{QP:()=>ee});let o=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],n=r.nextPart.get(t),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},n=/^\[(.+)\]$/,a=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:l(r,e)).classGroupId=t;return}if("function"==typeof e)return s(e)?void a(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{a(n,l(r,e),t,o)})})},l=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},s=e=>e.isThemeGetter,i=/\s+/;function c(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=d(e))&&(o&&(o+=" "),o+=r);return o}let d=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=d(e[o]))&&(t&&(t+=" "),t+=r);return t},u=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,f=/^\d+\/\d+$/,b=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,h=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,g=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,k=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,v=e=>f.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),x=e=>!!e&&Number.isInteger(Number(e)),j=e=>e.endsWith("%")&&w(e.slice(0,-1)),z=e=>b.test(e),E=()=>!0,C=e=>h.test(e)&&!g.test(e),P=()=>!1,A=e=>y.test(e),S=e=>k.test(e),M=e=>!O(e)&&!U(e),N=e=>V(e,Q,P),O=e=>m.test(e),_=e=>V(e,H,C),T=e=>V(e,J,w),L=e=>V(e,q,P),I=e=>V(e,Z,S),R=e=>V(e,Y,A),U=e=>p.test(e),W=e=>B(e,H),$=e=>B(e,X),D=e=>B(e,q),F=e=>B(e,Q),K=e=>B(e,Z),G=e=>B(e,Y,!0),V=(e,r,t)=>{let o=m.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},B=(e,r,t=!1)=>{let o=p.exec(e);return!!o&&(o[1]?r(o[1]):t)},q=e=>"position"===e||"percentage"===e,Z=e=>"image"===e||"url"===e,Q=e=>"length"===e||"size"===e||"bg-size"===e,H=e=>"length"===e,J=e=>"number"===e,X=e=>"family-name"===e,Y=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...r){let t,l,s,d=function(i){let c;return l=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}})((c=r.reduce((e,r)=>r(e),e())).cacheSize),parseClassName:(e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t,o=[],n=0,a=0,l=0;for(let t=0;t<e.length;t++){let s=e[t];if(0===n&&0===a){if(":"===s){o.push(e.slice(l,t)),l=t+1;continue}if("/"===s){r=t;continue}}"["===s?n++:"]"===s?n--:"("===s?a++:")"===s&&a--}let s=0===o.length?e:e.substring(l),i=(t=s).endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t;return{modifiers:o,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o})(c),sortModifiers:(e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}})(c),...(e=>{let r=(e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)a(t[e],o,e,r);return o})(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:l}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),o(t,r)||(e=>{if(n.test(e)){let r=n.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}})(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&l[e]?[...o,...l[e]]:o}}})(c)}).cache.get,s=t.cache.set,d=u,u(i)};function u(e){let r=l(e);if(r)return r;let o=((e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],s=e.trim().split(i),c="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:i,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=t(r);if(i){c=r+(c.length>0?" "+c:c);continue}let f=!!p,b=o(f?m.substring(0,p):m);if(!b){if(!f||!(b=o(m))){c=r+(c.length>0?" "+c:c);continue}f=!1}let h=a(d).join(":"),g=u?h+"!":h,y=g+b;if(l.includes(y))continue;l.push(y);let k=n(b,f);for(let e=0;e<k.length;++e){let r=k[e];l.push(g+r)}c=r+(c.length>0?" "+c:c)}return c})(e,t);return s(e,o),o}return function(){return d(c.apply(null,arguments))}}(()=>{let e=u("color"),r=u("font"),t=u("text"),o=u("font-weight"),n=u("tracking"),a=u("leading"),l=u("breakpoint"),s=u("container"),i=u("spacing"),c=u("radius"),d=u("shadow"),m=u("inset-shadow"),p=u("text-shadow"),f=u("drop-shadow"),b=u("blur"),h=u("perspective"),g=u("aspect"),y=u("ease"),k=u("animate"),C=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],A=()=>[...P(),U,O],S=()=>["auto","hidden","clip","visible","scroll"],V=()=>["auto","contain","none"],B=()=>[U,O,i],q=()=>[v,"full","auto",...B()],Z=()=>[x,"none","subgrid",U,O],Q=()=>["auto",{span:["full",x,U,O]},x,U,O],H=()=>[x,"auto",U,O],J=()=>["auto","min","max","fr",U,O],X=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Y=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...B()],er=()=>[v,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...B()],et=()=>[e,U,O],eo=()=>[...P(),D,L,{position:[U,O]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",F,N,{size:[U,O]}],el=()=>[j,W,_],es=()=>["","none","full",c,U,O],ei=()=>["",w,W,_],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[w,j,D,L],em=()=>["","none",b,U,O],ep=()=>["none",w,U,O],ef=()=>["none",w,U,O],eb=()=>[w,U,O],eh=()=>[v,"full",...B()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[E],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[M],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",w],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",v,O,U,g]}],container:["container"],columns:[{columns:[w,O,U,s]}],"break-after":[{"break-after":C()}],"break-before":[{"break-before":C()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:A()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:V()}],"overscroll-x":[{"overscroll-x":V()}],"overscroll-y":[{"overscroll-y":V()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:q()}],"inset-x":[{"inset-x":q()}],"inset-y":[{"inset-y":q()}],start:[{start:q()}],end:[{end:q()}],top:[{top:q()}],right:[{right:q()}],bottom:[{bottom:q()}],left:[{left:q()}],visibility:["visible","invisible","collapse"],z:[{z:[x,"auto",U,O]}],basis:[{basis:[v,"full","auto",s,...B()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,v,"auto","initial","none",O]}],grow:[{grow:["",w,U,O]}],shrink:[{shrink:["",w,U,O]}],order:[{order:[x,"first","last","none",U,O]}],"grid-cols":[{"grid-cols":Z()}],"col-start-end":[{col:Q()}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":Z()}],"row-start-end":[{row:Q()}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":J()}],"auto-rows":[{"auto-rows":J()}],gap:[{gap:B()}],"gap-x":[{"gap-x":B()}],"gap-y":[{"gap-y":B()}],"justify-content":[{justify:[...X(),"normal"]}],"justify-items":[{"justify-items":[...Y(),"normal"]}],"justify-self":[{"justify-self":["auto",...Y()]}],"align-content":[{content:["normal",...X()]}],"align-items":[{items:[...Y(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Y(),{baseline:["","last"]}]}],"place-content":[{"place-content":X()}],"place-items":[{"place-items":[...Y(),"baseline"]}],"place-self":[{"place-self":["auto",...Y()]}],p:[{p:B()}],px:[{px:B()}],py:[{py:B()}],ps:[{ps:B()}],pe:[{pe:B()}],pt:[{pt:B()}],pr:[{pr:B()}],pb:[{pb:B()}],pl:[{pl:B()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":B()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":B()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,W,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,U,T]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",j,O]}],"font-family":[{font:[$,O,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,U,O]}],"line-clamp":[{"line-clamp":[w,"none",U,T]}],leading:[{leading:[a,...B()]}],"list-image":[{"list-image":["none",U,O]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",U,O]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",U,_]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[w,"auto",U,O]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:B()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",U,O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",U,O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},x,U,O],radial:["",U,O],conic:[x,U,O]},K,I]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,U,O]}],"outline-w":[{outline:["",w,W,_]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",d,G,R]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",m,G,R]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[w,_]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",p,G,R]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[w,U,O]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[U,O]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",U,O]}],filter:[{filter:["","none",U,O]}],blur:[{blur:em()}],brightness:[{brightness:[w,U,O]}],contrast:[{contrast:[w,U,O]}],"drop-shadow":[{"drop-shadow":["","none",f,G,R]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",w,U,O]}],"hue-rotate":[{"hue-rotate":[w,U,O]}],invert:[{invert:["",w,U,O]}],saturate:[{saturate:[w,U,O]}],sepia:[{sepia:["",w,U,O]}],"backdrop-filter":[{"backdrop-filter":["","none",U,O]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[w,U,O]}],"backdrop-contrast":[{"backdrop-contrast":[w,U,O]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,U,O]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,U,O]}],"backdrop-invert":[{"backdrop-invert":["",w,U,O]}],"backdrop-opacity":[{"backdrop-opacity":[w,U,O]}],"backdrop-saturate":[{"backdrop-saturate":[w,U,O]}],"backdrop-sepia":[{"backdrop-sepia":["",w,U,O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":B()}],"border-spacing-x":[{"border-spacing-x":B()}],"border-spacing-y":[{"border-spacing-y":B()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",U,O]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",U,O]}],ease:[{ease:["linear","initial",y,U,O]}],delay:[{delay:[w,U,O]}],animate:[{animate:["none",k,U,O]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,U,O]}],"perspective-origin":[{"perspective-origin":A()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[U,O,"","none","gpu","cpu"]}],"transform-origin":[{origin:A()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",U,O]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":B()}],"scroll-mx":[{"scroll-mx":B()}],"scroll-my":[{"scroll-my":B()}],"scroll-ms":[{"scroll-ms":B()}],"scroll-me":[{"scroll-me":B()}],"scroll-mt":[{"scroll-mt":B()}],"scroll-mr":[{"scroll-mr":B()}],"scroll-mb":[{"scroll-mb":B()}],"scroll-ml":[{"scroll-ml":B()}],"scroll-p":[{"scroll-p":B()}],"scroll-px":[{"scroll-px":B()}],"scroll-py":[{"scroll-py":B()}],"scroll-ps":[{"scroll-ps":B()}],"scroll-pe":[{"scroll-pe":B()}],"scroll-pt":[{"scroll-pt":B()}],"scroll-pr":[{"scroll-pr":B()}],"scroll-pb":[{"scroll-pb":B()}],"scroll-pl":[{"scroll-pl":B()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",U,O]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[w,W,_,T]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,r,t)=>{t.d(r,{DX:()=>s,TL:()=>l});var o=t(2115),n=t(6101),a=t(5155);function l(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...a}=e;if(o.isValidElement(t)){var l;let e,s,i=(l=t,(s=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(s=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,r){let t={...r};for(let o in r){let n=e[o],a=r[o];/^on[A-Z]/.test(o)?n&&a?t[o]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...a}:"className"===o&&(t[o]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==o.Fragment&&(c.ref=r?(0,n.t)(r,i):i),o.cloneElement(t,c)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...l}=e,s=o.Children.toArray(n),i=s.find(c);if(i){let e=i.props.children,n=s.map(r=>r!==i?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...l,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var s=l("Slot"),i=Symbol("radix.slottable");function c(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},9946:(e,r,t)=>{t.d(r,{A:()=>i});var o=t(2115);let n=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,o.forwardRef)((e,r)=>{let{color:t="currentColor",size:n=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:u,...m}=e;return(0,o.createElement)("svg",{ref:r,...l,width:n,height:n,stroke:t,strokeWidth:i?24*Number(s)/Number(n):s,className:a("lucide",c),...!d&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[r,t]=e;return(0,o.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),i=(e,r)=>{let t=(0,o.forwardRef)((t,l)=>{let{className:i,...c}=t;return(0,o.createElement)(s,{ref:l,iconNode:r,className:a("lucide-".concat(n(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...c})});return t.displayName=n(e),t}},9991:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return b},PageNotFoundError:function(){return h},SP:function(){return m},ST:function(){return p},WEB_VITALS:function(){return t},execOnce:function(){return o},getDisplayName:function(){return i},getLocationOrigin:function(){return l},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return k}});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let r,t=!1;return function(){for(var o=arguments.length,n=Array(o),a=0;a<o;a++)n[a]=arguments[a];return t||(t=!0,r=e(...n)),r}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>n.test(e);function l(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function s(){let{href:e}=window.location,r=l();return e.substring(r.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function u(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await u(r.Component,r.ctx)}:{};let o=await e.getInitialProps(r);if(t&&c(t))return o;if(!o)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o}let m="undefined"!=typeof performance,p=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class b extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function k(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);