"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[908],{285:(e,s,r)=>{r.d(s,{$:()=>o});var a=r(5155),t=r(2115),n=r(9708),c=r(2085),i=r(9434);let l=(0,c.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=t.forwardRef((e,s)=>{let{className:r,variant:t,size:c,asChild:o=!1,...d}=e,m=o?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(l({variant:t,size:c,className:r})),ref:s,...d})});o.displayName="Button"},1399:(e,s,r)=>{r.d(s,{w:()=>p});var a=r(5155),t=r(6874),n=r.n(t),c=r(285),i=r(2523),l=r(9420),o=r(8883),d=r(4516),m=r(9946);let x=(0,m.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),h=(0,m.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),f=(0,m.A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),u={products:[{name:"Hospital Uniforms",href:"/products?category=hospital"},{name:"Hotel Uniforms",href:"/products?category=hotel"},{name:"Security Uniforms",href:"/products?category=security"},{name:"Industrial Uniforms",href:"/products?category=industrial"},{name:"Custom Uniforms",href:"/products?category=custom"}],company:[{name:"About Us",href:"/about"},{name:"Our Services",href:"/services"},{name:"Quality Assurance",href:"/quality"},{name:"Careers",href:"/careers"},{name:"Contact",href:"/contact"}],support:[{name:"Size Guide",href:"/size-guide"},{name:"Care Instructions",href:"/care-instructions"},{name:"Shipping Info",href:"/shipping"},{name:"Returns",href:"/returns"},{name:"FAQ",href:"/faq"}],legal:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"Refund Policy",href:"/refunds"}]};function p(){return(0,a.jsx)("footer",{className:"bg-muted/50 border-t border-border",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"h-8 w-8 bg-primary rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"I"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-bold text-lg leading-none",children:"IDC Uniform"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Professional Uniforms"})]})]}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4 max-w-md",children:"Professional uniform solutions for hospitals, hotels, security, industrial, and custom applications. Quality you can trust, service you can count on."}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"+1234567890"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"123 Business St, City, State 12345"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(x,{className:"h-4 w-4"})}),(0,a.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(h,{className:"h-4 w-4"})}),(0,a.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(f,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-4",children:"Products"}),(0,a.jsx)("ul",{className:"space-y-2",children:u.products.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-4",children:"Company"}),(0,a.jsx)("ul",{className:"space-y-2",children:u.company.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-4",children:"Support"}),(0,a.jsx)("ul",{className:"space-y-2",children:u.support.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.name})},e.name))})]})]}),(0,a.jsx)("div",{className:"border-t border-border mt-8 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Stay Updated"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Subscribe to our newsletter for the latest uniform trends and offers."})]}),(0,a.jsxs)("div",{className:"flex w-full md:w-auto gap-2",children:[(0,a.jsx)(i.p,{placeholder:"Enter your email",className:"md:w-64"}),(0,a.jsx)(c.$,{children:"Subscribe"})]})]})}),(0,a.jsxs)("div",{className:"border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 IDC Uniform. All rights reserved."}),(0,a.jsx)("div",{className:"flex space-x-4",children:u.legal.map(e=>(0,a.jsx)(n(),{href:e.href,className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:e.name},e.name))})]})]})})}},1440:(e,s,r)=>{r.d(s,{Navigation:()=>j});var a=r(5155),t=r(2115),n=r(2098),c=r(3509),i=r(1362),l=r(285);function o(){let{theme:e,setTheme:s}=(0,i.D)(),[r,o]=t.useState(!1);return(t.useEffect(()=>{o(!0)},[]),r)?(0,a.jsxs)(l.$,{variant:"ghost",size:"icon",onClick:()=>s("light"===e?"dark":"light"),className:"w-9 h-9",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(c.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}):(0,a.jsxs)(l.$,{variant:"ghost",size:"icon",className:"w-9 h-9",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}var d=r(9420),m=r(8883),x=r(4416),h=r(4783),f=r(6874),u=r.n(f);let p=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Contact",href:"/contact"}];function j(){let[e,s]=(0,t.useState)(!1);return(0,a.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center justify-between py-2 text-sm border-b border-border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"+1234567890"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Mon-Fri 9AM-6PM"}),(0,a.jsx)(o,{})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(u(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 bg-primary rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"I"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-bold text-lg leading-none",children:"IDC Uniform"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Professional Uniforms"})]})]})}),(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:p.map(e=>(0,a.jsx)(u(),{href:e.href,className:"text-sm font-medium transition-colors hover:text-primary",children:e.name},e.name))}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsx)(u(),{href:"/contact",children:"Get Quote"})}),(0,a.jsx)(l.$,{size:"sm",asChild:!0,children:(0,a.jsx)(u(),{href:"/contact",children:"Contact Us"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 md:hidden",children:[(0,a.jsx)(o,{}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",onClick:()=>s(!e),children:e?(0,a.jsx)(x.A,{className:"h-5 w-5"}):(0,a.jsx)(h.A,{className:"h-5 w-5"})})]})]}),e&&(0,a.jsx)("div",{className:"md:hidden border-t border-border",children:(0,a.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[p.map(e=>(0,a.jsx)(u(),{href:e.href,className:"block px-3 py-2 text-base font-medium transition-colors hover:text-primary hover:bg-accent rounded-md",onClick:()=>s(!1),children:e.name},e.name)),(0,a.jsxs)("div",{className:"flex flex-col space-y-2 px-3 pt-4",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",asChild:!0,children:(0,a.jsx)(u(),{href:"/contact",children:"Get Quote"})}),(0,a.jsx)(l.$,{size:"sm",className:"w-full",asChild:!0,children:(0,a.jsx)(u(),{href:"/contact",children:"Contact Us"})})]})]})})]})})}},2523:(e,s,r)=>{r.d(s,{p:()=>c});var a=r(5155),t=r(2115),n=r(9434);let c=t.forwardRef((e,s)=>{let{className:r,type:t,...c}=e;return(0,a.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...c})});c.displayName="Input"},4516:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6695:(e,s,r)=>{r.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>c,aR:()=>i,wL:()=>m});var a=r(5155),t=r(2115),n=r(9434);let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",r),...t})});c.displayName="Card";let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...t})});i.displayName="CardHeader";let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});l.displayName="CardTitle";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...t})});o.displayName="CardDescription";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...t})});d.displayName="CardContent";let m=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...t})});m.displayName="CardFooter"},9434:(e,s,r)=>{r.d(s,{$g:()=>c,L4:()=>i,cn:()=>n,zp:()=>l});var a=r(2596),t=r(9688);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}function c(e){let s="string"==typeof e?parseFloat(e):e;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s)}function i(e,s){return"Hello IDC Uniform! \uD83D\uDC4B\n\nI'm interested in:\n\uD83D\uDCE6 Product: ".concat(e.name,"\n\uD83C\uDFF7️ Product Code: ").concat(e.id,"\n\uD83D\uDCCF Size: ").concat(s.size,"\n\uD83D\uDD22 Quantity: ").concat(s.quantity,"\n\uD83C\uDFA8 Color: ").concat(s.color||"Standard","\n\nCustomer Details:\n\uD83D\uDC64 Name: ").concat(s.customerName,"\n\uD83D\uDCF1 Phone: ").concat(s.customerPhone,"\n\uD83C\uDFE2 Company: ").concat(s.companyName||"Individual","\n\nAdditional Notes:\n").concat(s.notes||"None","\n\nPlease provide pricing and availability.")}function l(e,s){let r=encodeURIComponent(e);return"https://wa.me/".concat(s,"?text=").concat(r)}}}]);