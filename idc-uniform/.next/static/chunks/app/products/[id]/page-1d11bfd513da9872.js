(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[403],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>c});var r=a(5155),t=a(2115),n=a(9708),l=a(2085),i=a(9434);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,size:l,asChild:c=!1,...d}=e,m=c?n.DX:"button";return(0,r.jsx)(m,{className:(0,i.cn)(o({variant:t,size:l,className:a})),ref:s,...d})});c.displayName="Button"},1366:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},1440:(e,s,a)=>{"use strict";a.d(s,{Navigation:()=>j});var r=a(5155),t=a(2115),n=a(2098),l=a(3509),i=a(1362),o=a(285);function c(){let{theme:e,setTheme:s}=(0,i.D)(),[a,c]=t.useState(!1);return(t.useEffect(()=>{c(!0)},[]),a)?(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",onClick:()=>s("light"===e?"dark":"light"),className:"w-9 h-9",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(l.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}):(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"w-9 h-9",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}var d=a(9420),m=a(8883),u=a(4416),f=a(4783),h=a(6874),x=a.n(h);let p=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Contact",href:"/contact"}];function j(){let[e,s]=(0,t.useState)(!1);return(0,r.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center justify-between py-2 text-sm border-b border-border",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"+1234567890"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Mon-Fri 9AM-6PM"}),(0,r.jsx)(c,{})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(x(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-8 w-8 bg-primary rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"I"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"font-bold text-lg leading-none",children:"IDC Uniform"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Professional Uniforms"})]})]})}),(0,r.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:p.map(e=>(0,r.jsx)(x(),{href:e.href,className:"text-sm font-medium transition-colors hover:text-primary",children:e.name},e.name))}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(x(),{href:"/contact",children:"Get Quote"})}),(0,r.jsx)(o.$,{size:"sm",asChild:!0,children:(0,r.jsx)(x(),{href:"/contact",children:"Contact Us"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 md:hidden",children:[(0,r.jsx)(c,{}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",onClick:()=>s(!e),children:e?(0,r.jsx)(u.A,{className:"h-5 w-5"}):(0,r.jsx)(f.A,{className:"h-5 w-5"})})]})]}),e&&(0,r.jsx)("div",{className:"md:hidden border-t border-border",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[p.map(e=>(0,r.jsx)(x(),{href:e.href,className:"block px-3 py-2 text-base font-medium transition-colors hover:text-primary hover:bg-accent rounded-md",onClick:()=>s(!1),children:e.name},e.name)),(0,r.jsxs)("div",{className:"flex flex-col space-y-2 px-3 pt-4",children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"w-full",asChild:!0,children:(0,r.jsx)(x(),{href:"/contact",children:"Get Quote"})}),(0,r.jsx)(o.$,{size:"sm",className:"w-full",asChild:!0,children:(0,r.jsx)(x(),{href:"/contact",children:"Contact Us"})})]})]})})]})})}},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var r=a(5155),t=a(2115),n=a(9434);let l=t.forwardRef((e,s)=>{let{className:a,type:t,...l}=e;return(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...l})});l.displayName="Input"},2597:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.bind(a,1440)),Promise.resolve().then(a.bind(a,6617))},6617:(e,s,a)=>{"use strict";a.d(s,{WhatsAppContact:()=>u});var r=a(5155),t=a(2115),n=a(285),l=a(2523),i=a(8979),o=a(9409),c=a(6695),d=a(9434),m=a(1366);function u(e){let{product:s,companyWhatsApp:a}=e,[u,f]=(0,t.useState)({size:"",color:s.colors[0]||"",quantity:1,customerName:"",customerPhone:"",companyName:"",notes:""}),h=(e,s)=>{f(a=>({...a,[e]:s}))},x=u.size&&u.customerName&&u.customerPhone;return(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),"Contact via WhatsApp"]}),(0,r.jsx)(c.BT,{children:"Fill in your details to get a quick quote via WhatsApp"})]}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"size",children:"Size *"}),(0,r.jsxs)(o.l6,{onValueChange:e=>h("size",e),children:[(0,r.jsx)(o.bq,{children:(0,r.jsx)(o.yv,{placeholder:"Select size"})}),(0,r.jsx)(o.gC,{children:s.sizes.map(e=>(0,r.jsx)(o.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"color",children:"Color"}),(0,r.jsxs)(o.l6,{value:u.color,onValueChange:e=>h("color",e),children:[(0,r.jsx)(o.bq,{children:(0,r.jsx)(o.yv,{placeholder:"Select color"})}),(0,r.jsx)(o.gC,{children:s.colors.map(e=>(0,r.jsx)(o.eb,{value:e,children:e},e))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"quantity",children:"Quantity"}),(0,r.jsx)(l.p,{id:"quantity",type:"number",min:"1",value:u.quantity,onChange:e=>h("quantity",parseInt(e.target.value)||1)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"customerName",children:"Your Name *"}),(0,r.jsx)(l.p,{id:"customerName",value:u.customerName,onChange:e=>h("customerName",e.target.value),placeholder:"Enter your full name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"customerPhone",children:"Phone Number *"}),(0,r.jsx)(l.p,{id:"customerPhone",value:u.customerPhone,onChange:e=>h("customerPhone",e.target.value),placeholder:"Enter your phone number"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"companyName",children:"Company Name (Optional)"}),(0,r.jsx)(l.p,{id:"companyName",value:u.companyName,onChange:e=>h("companyName",e.target.value),placeholder:"Enter your company name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i.J,{htmlFor:"notes",children:"Additional Notes (Optional)"}),(0,r.jsx)(l.p,{id:"notes",value:u.notes,onChange:e=>h("notes",e.target.value),placeholder:"Any special requirements or questions"})]}),(0,r.jsxs)(n.$,{onClick:()=>{if(!u.size||!u.customerName||!u.customerPhone)return void alert("Please fill in all required fields");let e=(0,d.L4)(s,u),r=(0,d.zp)(e,a);window.open(r,"_blank")},disabled:!x,className:"w-full bg-green-600 hover:bg-green-700 text-white",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Contact via WhatsApp"]})]})]})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>i,wL:()=>m});var r=a(5155),t=a(2115),n=a(9434);let l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",a),...t})});l.displayName="Card";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...t})});i.displayName="CardHeader";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});o.displayName="CardTitle";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...t})});c.displayName="CardDescription";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",a),...t})});d.displayName="CardContent";let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",a),...t})});m.displayName="CardFooter"},8979:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var r=a(5155),t=a(2115),n=a(3655),l=t.forwardRef((e,s)=>(0,r.jsx)(n.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var i=a(2085),o=a(9434);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(l,{ref:s,className:(0,o.cn)(c(),a),...t})});d.displayName=l.displayName},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>u,eb:()=>p,gC:()=>x,l6:()=>d,yv:()=>m});var r=a(5155),t=a(2115),n=a(1396),l=a(6474),i=a(7863),o=a(5196),c=a(9434);let d=n.bL;n.YJ;let m=n.WT,u=t.forwardRef((e,s)=>{let{className:a,children:t,...i}=e;return(0,r.jsxs)(n.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[t,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=n.l9.displayName;let f=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(n.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});f.displayName=n.PP.displayName;let h=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(n.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});h.displayName=n.wn.displayName;let x=t.forwardRef((e,s)=>{let{className:a,children:t,position:l="popper",...i}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-border bg-background text-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...i,children:[(0,r.jsx)(f,{}),(0,r.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(h,{})]})})});x.displayName=n.UC.displayName,t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(n.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=n.JU.displayName;let p=t.forwardRef((e,s)=>{let{className:a,children:t,...l}=e;return(0,r.jsxs)(n.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:t})]})});p.displayName=n.q7.displayName,t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(n.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=n.wv.displayName},9434:(e,s,a)=>{"use strict";a.d(s,{$g:()=>l,L4:()=>i,cn:()=>n,zp:()=>o});var r=a(2596),t=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,r.$)(s))}function l(e){let s="string"==typeof e?parseFloat(e):e;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s)}function i(e,s){return"Hello IDC Uniform! \uD83D\uDC4B\n\nI'm interested in:\n\uD83D\uDCE6 Product: ".concat(e.name,"\n\uD83C\uDFF7️ Product Code: ").concat(e.id,"\n\uD83D\uDCCF Size: ").concat(s.size,"\n\uD83D\uDD22 Quantity: ").concat(s.quantity,"\n\uD83C\uDFA8 Color: ").concat(s.color||"Standard","\n\nCustomer Details:\n\uD83D\uDC64 Name: ").concat(s.customerName,"\n\uD83D\uDCF1 Phone: ").concat(s.customerPhone,"\n\uD83C\uDFE2 Company: ").concat(s.companyName||"Individual","\n\nAdditional Notes:\n").concat(s.notes||"None","\n\nPlease provide pricing and availability.")}function o(e,s){let a=encodeURIComponent(e);return"https://wa.me/".concat(s,"?text=").concat(a)}}},e=>{e.O(0,[911,868,441,964,358],()=>e(e.s=2597)),_N_E=e.O()}]);