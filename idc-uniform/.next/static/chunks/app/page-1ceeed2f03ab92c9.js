(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345,763,974],{285:(e,s,n)=>{"use strict";n.d(s,{$:()=>l});var r=n(5155),t=n(2115),a=n(9708),c=n(2085),i=n(9434);let o=(0,c.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=t.forwardRef((e,s)=>{let{className:n,variant:t,size:c,asChild:l=!1,...d}=e,m=l?a.DX:"button";return(0,r.jsx)(m,{className:(0,i.cn)(o({variant:t,size:c,className:n})),ref:s,...d})});l.displayName="Button"},1440:(e,s,n)=>{"use strict";n.d(s,{Navigation:()=>b});var r=n(5155),t=n(2115),a=n(2098),c=n(3509),i=n(1362),o=n(285);function l(){let{theme:e,setTheme:s}=(0,i.D)(),[n,l]=t.useState(!1);return(t.useEffect(()=>{l(!0)},[]),n)?(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",onClick:()=>s("light"===e?"dark":"light"),className:"w-9 h-9",children:[(0,r.jsx)(a.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(c.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}):(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"w-9 h-9",children:[(0,r.jsx)(a.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}var d=n(9420),m=n(8883),u=n(4416),h=n(4783),x=n(6874),f=n.n(x);let p=[{name:"Home",href:"/"},{name:"Products",href:"/products"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Contact",href:"/contact"}];function b(){let[e,s]=(0,t.useState)(!1);return(0,r.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center justify-between py-2 text-sm border-b border-border",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"+1234567890"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Mon-Fri 9AM-6PM"}),(0,r.jsx)(l,{})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(f(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-8 w-8 bg-primary rounded-md flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"I"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"font-bold text-lg leading-none",children:"IDC Uniform"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Professional Uniforms"})]})]})}),(0,r.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:p.map(e=>(0,r.jsx)(f(),{href:e.href,className:"text-sm font-medium transition-colors hover:text-primary",children:e.name},e.name))}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)(f(),{href:"/contact",children:"Get Quote"})}),(0,r.jsx)(o.$,{size:"sm",asChild:!0,children:(0,r.jsx)(f(),{href:"/contact",children:"Contact Us"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 md:hidden",children:[(0,r.jsx)(l,{}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",onClick:()=>s(!e),children:e?(0,r.jsx)(u.A,{className:"h-5 w-5"}):(0,r.jsx)(h.A,{className:"h-5 w-5"})})]})]}),e&&(0,r.jsx)("div",{className:"md:hidden border-t border-border",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[p.map(e=>(0,r.jsx)(f(),{href:e.href,className:"block px-3 py-2 text-base font-medium transition-colors hover:text-primary hover:bg-accent rounded-md",onClick:()=>s(!1),children:e.name},e.name)),(0,r.jsxs)("div",{className:"flex flex-col space-y-2 px-3 pt-4",children:[(0,r.jsx)(o.$,{variant:"outline",size:"sm",className:"w-full",asChild:!0,children:(0,r.jsx)(f(),{href:"/contact",children:"Get Quote"})}),(0,r.jsx)(o.$,{size:"sm",className:"w-full",asChild:!0,children:(0,r.jsx)(f(),{href:"/contact",children:"Contact Us"})})]})]})})]})})}},6788:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,6874,23)),Promise.resolve().then(n.bind(n,1440))},9434:(e,s,n)=>{"use strict";n.d(s,{$g:()=>c,L4:()=>i,cn:()=>a,zp:()=>o});var r=n(2596),t=n(9688);function a(){for(var e=arguments.length,s=Array(e),n=0;n<e;n++)s[n]=arguments[n];return(0,t.QP)((0,r.$)(s))}function c(e){let s="string"==typeof e?parseFloat(e):e;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s)}function i(e,s){return"Hello IDC Uniform! \uD83D\uDC4B\n\nI'm interested in:\n\uD83D\uDCE6 Product: ".concat(e.name,"\n\uD83C\uDFF7️ Product Code: ").concat(e.id,"\n\uD83D\uDCCF Size: ").concat(s.size,"\n\uD83D\uDD22 Quantity: ").concat(s.quantity,"\n\uD83C\uDFA8 Color: ").concat(s.color||"Standard","\n\nCustomer Details:\n\uD83D\uDC64 Name: ").concat(s.customerName,"\n\uD83D\uDCF1 Phone: ").concat(s.customerPhone,"\n\uD83C\uDFE2 Company: ").concat(s.companyName||"Individual","\n\nAdditional Notes:\n").concat(s.notes||"None","\n\nPlease provide pricing and availability.")}function o(e,s){let n=encodeURIComponent(e);return"https://wa.me/".concat(s,"?text=").concat(n)}}},e=>{e.O(0,[911,441,964,358],()=>e(e.s=6788)),_N_E=e.O()}]);