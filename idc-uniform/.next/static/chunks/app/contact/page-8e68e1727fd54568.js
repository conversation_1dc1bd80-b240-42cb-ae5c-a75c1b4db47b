(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{1366:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]])},3655:(e,s,a)=>{"use strict";a.d(s,{hO:()=>c,sG:()=>t});var l=a(2115),n=a(7650),r=a(9708),i=a(5155),t=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let a=(0,r.TL)(`Primitive.${s}`),n=l.forwardRef((e,l)=>{let{asChild:n,...r}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?a:s,{...r,ref:l})});return n.displayName=`Primitive.${s}`,{...e,[s]:n}},{});function c(e,s){e&&n.flushSync(()=>e.dispatchEvent(s))}},4060:(e,s,a)=>{Promise.resolve().then(a.bind(a,8346))},8346:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var l=a(5155),n=a(2115),r=a(1440),i=a(1399),t=a(285),c=a(2523),o=a(8979),d=a(6695),m=a(1366),u=a(9420),x=a(8883),h=a(4516);let p=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),j=JSON.parse('{"Q":{"b_":"+1234567890","Rp":"<EMAIL>"},"j":[{"id":"loc1","name":"Main Office","address":"123 Business St, City, State 12345","phone":"+1234567890","hours":"Mon-Fri 9AM-6PM, Sat 9AM-2PM","mapUrl":"https://maps.google.com"},{"id":"loc2","name":"Warehouse & Distribution","address":"456 Industrial Ave, City, State 12345","phone":"+1234567891","hours":"Mon-Fri 8AM-5PM","mapUrl":"https://maps.google.com"}]}');function g(){let[e,s]=(0,n.useState)({name:"",email:"",phone:"",company:"",message:""}),a=(e,a)=>{s(s=>({...s,[e]:a}))};return(0,l.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,l.jsx)(r.Navigation,{}),(0,l.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h1",{className:"text-4xl lg:text-5xl font-bold mb-4",children:"Contact Us"}),(0,l.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Get in touch with our team for custom quotes, bulk orders, or any questions about our uniform solutions"})]}),(0,l.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,l.jsxs)(d.Zp,{children:[(0,l.jsxs)(d.aR,{children:[(0,l.jsx)(d.ZB,{children:"Send us a Message"}),(0,l.jsx)(d.BT,{children:"Fill out the form below and we'll get back to you within 24 hours"})]}),(0,l.jsx)(d.Wu,{children:(0,l.jsxs)("form",{onSubmit:s=>{s.preventDefault(),console.log("Form submitted:",e),alert("Thank you for your message! We'll get back to you soon.")},className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"name",children:"Name *"}),(0,l.jsx)(c.p,{id:"name",value:e.name,onChange:e=>a("name",e.target.value),required:!0})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"email",children:"Email *"}),(0,l.jsx)(c.p,{id:"email",type:"email",value:e.email,onChange:e=>a("email",e.target.value),required:!0})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"phone",children:"Phone"}),(0,l.jsx)(c.p,{id:"phone",value:e.phone,onChange:e=>a("phone",e.target.value)})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"company",children:"Company"}),(0,l.jsx)(c.p,{id:"company",value:e.company,onChange:e=>a("company",e.target.value)})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"message",children:"Message *"}),(0,l.jsx)("textarea",{id:"message",className:"flex min-h-[120px] w-full rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:e.message,onChange:e=>a("message",e.target.value),placeholder:"Tell us about your uniform requirements...",required:!0})]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(t.$,{type:"submit",className:"flex-1",children:"Send Message"}),(0,l.jsxs)(t.$,{type:"button",onClick:()=>{let s=encodeURIComponent("Hello IDC Uniform! \uD83D\uDC4B\n\nI'm interested in learning more about your uniform solutions.\n\nContact Details:\n\uD83D\uDC64 Name: ".concat(e.name,"\n\uD83D\uDCE7 Email: ").concat(e.email,"\n\uD83D\uDCF1 Phone: ").concat(e.phone,"\n\uD83C\uDFE2 Company: ").concat(e.company||"Individual","\n\nMessage:\n").concat(e.message||"Please contact me to discuss uniform requirements.","\n\nLooking forward to hearing from you!")),a="https://wa.me/".concat(j.Q.b_,"?text=").concat(s);window.open(a,"_blank")},className:"flex-1 bg-green-600 hover:bg-green-700",children:[(0,l.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"WhatsApp"]})]})]})})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(d.Zp,{children:[(0,l.jsxs)(d.aR,{children:[(0,l.jsx)(d.ZB,{children:"Get in Touch"}),(0,l.jsx)(d.BT,{children:"Multiple ways to reach our team"})]}),(0,l.jsxs)(d.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(u.A,{className:"h-5 w-5 text-primary"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Phone"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:j.Q.b_})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(x.A,{className:"h-5 w-5 text-primary"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Email"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:j.Q.Rp})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"WhatsApp"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:"Quick responses via WhatsApp"})]})]})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:j.j.map(e=>(0,l.jsxs)(d.Zp,{children:[(0,l.jsx)(d.aR,{children:(0,l.jsx)(d.ZB,{className:"text-lg",children:e.name})}),(0,l.jsxs)(d.Wu,{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,l.jsx)(h.A,{className:"h-5 w-5 text-primary mt-0.5"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Address"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:e.address})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(p,{className:"h-5 w-5 text-primary"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Hours"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:e.hours})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)(u.A,{className:"h-5 w-5 text-primary"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Phone"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:e.phone})]})]})]})]},e.id))})]})]})]}),(0,l.jsx)(i.w,{})]})}},8979:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var l=a(5155),n=a(2115),r=a(3655),i=n.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var t=a(2085),c=a(9434);let o=(0,t.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,l.jsx)(i,{ref:s,className:(0,c.cn)(o(),a),...n})});d.displayName=i.displayName}},e=>{e.O(0,[911,908,441,964,358],()=>e(e.s=4060)),_N_E=e.O()}]);