(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>m,N:()=>c});var a=r(2115),n=(e,t,r,a,n,s,l,o)=>{let i=document.documentElement,m=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,a=r&&s?n.map(e=>s[e]||e):n;r?(i.classList.remove(...a),i.classList.add(s&&s[t]?s[t]:t)):i.setAttribute(e,t)}),r=t,o&&m.includes(r)&&(i.style.colorScheme=r)}if(a)c(a);else try{let e=localStorage.getItem(t)||r,a=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(a)}catch(e){}},s=["light","dark"],l="(prefers-color-scheme: dark)",o=a.createContext(void 0),i={setTheme:e=>{},themes:[]},m=()=>{var e;return null!=(e=a.useContext(o))?e:i},c=e=>a.useContext(o)?a.createElement(a.Fragment,null,e.children):a.createElement(u,{...e}),d=["light","dark"],u=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:i=!0,storageKey:m="theme",themes:c=d,defaultTheme:u=n?"system":"light",attribute:v="data-theme",value:p,children:g,nonce:S,scriptProps:E}=e,[T,k]=a.useState(()=>y(m,u)),[w,C]=a.useState(()=>"system"===T?f():T),_=p?Object.values(p):c,L=a.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=f());let a=p?p[t]:t,l=r?b(S):null,o=document.documentElement,m=e=>{"class"===e?(o.classList.remove(..._),a&&o.classList.add(a)):e.startsWith("data-")&&(a?o.setAttribute(e,a):o.removeAttribute(e))};if(Array.isArray(v)?v.forEach(m):m(v),i){let e=s.includes(u)?u:null,r=s.includes(t)?t:e;o.style.colorScheme=r}null==l||l()},[S]),N=a.useCallback(e=>{let t="function"==typeof e?e(T):e;k(t);try{localStorage.setItem(m,t)}catch(e){}},[T]),A=a.useCallback(e=>{C(f(e)),"system"===T&&n&&!t&&L("system")},[T,t]);a.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),a.useEffect(()=>{let e=e=>{e.key===m&&(e.newValue?k(e.newValue):N(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[N]),a.useEffect(()=>{L(null!=t?t:T)},[t,T]);let P=a.useMemo(()=>({theme:T,setTheme:N,forcedTheme:t,resolvedTheme:"system"===T?w:T,themes:n?[...c,"system"]:c,systemTheme:n?w:void 0}),[T,N,t,w,n,c]);return a.createElement(o.Provider,{value:P},a.createElement(h,{forcedTheme:t,storageKey:m,attribute:v,enableSystem:n,enableColorScheme:i,defaultTheme:u,value:p,themes:c,nonce:S,scriptProps:E}),g)},h=a.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:s,enableSystem:l,enableColorScheme:o,defaultTheme:i,value:m,themes:c,nonce:d,scriptProps:u}=e,h=JSON.stringify([s,r,i,t,c,m,l,o]).slice(1,-1);return a.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(h,")")}})}),y=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},b=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},f=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},1483:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var a=r(5155);r(2115);var n=r(1362);function s(e){let{children:t,...r}=e;return(0,a.jsx)(n.N,{attribute:"data-theme",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!1,...r,children:t})}},1904:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9091,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,1483))},9091:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}}},e=>{e.O(0,[725,441,964,358],()=>e(e.s=1904)),_N_E=e.O()}]);