"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[868],{1396:(e,t,n)=>{n.d(t,{UC:()=>ru,YJ:()=>rc,In:()=>rl,q7:()=>rf,VF:()=>rh,p4:()=>rp,JU:()=>rd,ZL:()=>ra,bL:()=>rr,wn:()=>rm,PP:()=>rv,wv:()=>rg,l9:()=>ro,WT:()=>ri,LM:()=>rs});var r,o,i,l,a=n(2115),u=n.t(a,2),s=n(7650);function c(e,[t,n]){return Math.min(n,Math.max(t,e))}function d(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function f(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function p(e,t){var n=f(e,t,"get");return n.get?n.get.call(e):n.value}function h(e,t,n){var r=f(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var v=n(5155);function m(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),i=n.length;n=[...n,r];let l=t=>{let{scope:n,children:r,...l}=t,u=n?.[e]?.[i]||o,s=a.useMemo(()=>l,Object.values(l));return(0,v.jsx)(u.Provider,{value:s,children:r})};return l.displayName=t+"Provider",[l,function(n,l){let u=l?.[e]?.[i]||o,s=a.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var g=n(6101),y=n(9708),w=new WeakMap;function x(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=b(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function b(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap,class e extends Map{set(e,t){return w.get(this)&&(this.has(e)?p(this,o)[p(this,o).indexOf(e)]=e:p(this,o).push(e)),super.set(e,t),this}insert(e,t,n){let r,i=this.has(t),l=p(this,o).length,a=b(e),u=a>=0?a:l+a,s=u<0||u>=l?-1:u;if(s===this.size||i&&s===this.size-1||-1===s)return this.set(t,n),this;let c=this.size+ +!i;a<0&&u++;let d=[...p(this,o)],f=!1;for(let e=u;e<c;e++)if(u===e){let o=d[e];d[e]===t&&(o=d[e+1]),i&&this.delete(t),r=this.get(o),this.set(t,n)}else{f||d[e-1]!==t||(f=!0);let n=d[f?e:e-1],o=r;r=this.get(n),this.delete(n),this.set(n,o)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=p(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=p(this,o).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=p(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=p(this,o).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return h(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&p(this,o).splice(p(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=x(p(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=x(p(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return p(this,o).indexOf(e)}keyAt(e){return x(p(this,o),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,l=null!=o?o:this.at(0);for(let e of this)l=0===i&&1===t.length?e:Reflect.apply(r,this,[l,e,i,this]),i++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,o,{writable:!0,value:void 0}),h(this,o,[...super.keys()]),w.set(this,!0)}};var E=a.createContext(void 0),S=n(3655);function C(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var R="dismissableLayer.update",A=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),T=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:s,onInteractOutside:c,onDismiss:f,...p}=e,h=a.useContext(A),[m,y]=a.useState(null),w=null!=(r=null==m?void 0:m.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=a.useState({}),b=(0,g.s)(t,e=>y(e)),E=Array.from(h.layers),[T]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),P=E.indexOf(T),M=m?E.indexOf(m):-1,N=h.layersWithOutsidePointerEventsDisabled.size>0,O=M>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=C(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){k("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));O&&!n&&(null==u||u(e),null==c||c(e),e.defaultPrevented||null==f||f())},w),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=C(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&k("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==s||s(e),null==c||c(e),e.defaultPrevented||null==f||f())},w);return!function(e,t=globalThis?.document){let n=C(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===h.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},w),a.useEffect(()=>{if(m)return o&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(i=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(m)),h.layers.add(m),L(),()=>{o&&1===h.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=i)}},[m,w,o,h]),a.useEffect(()=>()=>{m&&(h.layers.delete(m),h.layersWithOutsidePointerEventsDisabled.delete(m),L())},[m,h]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(R,e),()=>document.removeEventListener(R,e)},[]),(0,v.jsx)(S.sG.div,{...p,ref:b,style:{pointerEvents:N?O?"auto":"none":void 0,...e.style},onFocusCapture:d(e.onFocusCapture,D.onFocusCapture),onBlurCapture:d(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:d(e.onPointerDownCapture,j.onPointerDownCapture)})});function L(){let e=new CustomEvent(R);document.dispatchEvent(e)}function k(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,S.hO)(i,l):i.dispatchEvent(l)}T.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(A),r=a.useRef(null),o=(0,g.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(S.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var P=0;function M(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var N="focusScope.autoFocusOnMount",O="focusScope.autoFocusOnUnmount",j={bubbles:!1,cancelable:!0},D=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,s]=a.useState(null),c=C(o),d=C(i),f=a.useRef(null),p=(0,g.s)(t,e=>s(e)),h=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(h.paused||!u)return;let t=e.target;u.contains(t)?f.current=t:F(f.current,{select:!0})},t=function(e){if(h.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||F(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&F(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,h.paused]),a.useEffect(()=>{if(u){H.add(h);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(N,j);u.addEventListener(N,c),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(F(r,{select:t}),document.activeElement!==n)return}(I(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&F(u))}return()=>{u.removeEventListener(N,c),setTimeout(()=>{let t=new CustomEvent(O,j);u.addEventListener(O,d),u.dispatchEvent(t),t.defaultPrevented||F(null!=e?e:document.body,{select:!0}),u.removeEventListener(O,d),H.remove(h)},0)}}},[u,c,d,h]);let m=a.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=I(e);return[W(t,e),W(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&F(i,{select:!0})):(e.preventDefault(),n&&F(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,v.jsx)(S.sG.div,{tabIndex:-1,...l,ref:p,onKeyDown:m})});function I(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function W(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function F(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}D.displayName="FocusScope";var H=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=B(e,t)).unshift(t)},remove(t){var n;null==(n=(e=B(e,t))[0])||n.resume()}}}();function B(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var z=globalThis?.document?a.useLayoutEffect:()=>{},_=u[" useId ".trim().toString()]||(()=>void 0),V=0;function G(e){let[t,n]=a.useState(_());return z(()=>{e||n(e=>e??String(V++))},[e]),e||(t?`radix-${t}`:"")}let K=["top","right","bottom","left"],Y=Math.min,U=Math.max,X=Math.round,$=Math.floor,q=e=>({x:e,y:e}),Z={left:"right",right:"left",bottom:"top",top:"bottom"},J={start:"end",end:"start"};function Q(e,t){return"function"==typeof e?e(t):e}function ee(e){return e.split("-")[0]}function et(e){return e.split("-")[1]}function en(e){return"x"===e?"y":"x"}function er(e){return"y"===e?"height":"width"}let eo=new Set(["top","bottom"]);function ei(e){return eo.has(ee(e))?"y":"x"}function el(e){return e.replace(/start|end/g,e=>J[e])}let ea=["left","right"],eu=["right","left"],es=["top","bottom"],ec=["bottom","top"];function ed(e){return e.replace(/left|right|bottom|top/g,e=>Z[e])}function ef(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ep(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function eh(e,t,n){let r,{reference:o,floating:i}=e,l=ei(t),a=en(ei(t)),u=er(a),s=ee(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(et(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let ev=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=eh(s,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=eh(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function em(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Q(t,e),h=ef(p),v=a[f?"floating"===d?"reference":"floating":d],m=ep(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=ep(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-x.top+h.top)/w.y,bottom:(x.bottom-m.bottom+h.bottom)/w.y,left:(m.left-x.left+h.left)/w.x,right:(x.right-m.right+h.right)/w.x}}function eg(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ey(e){return K.some(t=>e[t]>=0)}let ew=new Set(["left","top"]);async function ex(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=ee(n),a=et(n),u="y"===ei(n),s=ew.has(l)?-1:1,c=i&&u?-1:1,d=Q(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*c,y:f*s}:{x:f*s,y:p*c}}function eb(){return"undefined"!=typeof window}function eE(e){return eR(e)?(e.nodeName||"").toLowerCase():"#document"}function eS(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eC(e){var t;return null==(t=(eR(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eR(e){return!!eb()&&(e instanceof Node||e instanceof eS(e).Node)}function eA(e){return!!eb()&&(e instanceof Element||e instanceof eS(e).Element)}function eT(e){return!!eb()&&(e instanceof HTMLElement||e instanceof eS(e).HTMLElement)}function eL(e){return!!eb()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eS(e).ShadowRoot)}let ek=new Set(["inline","contents"]);function eP(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ez(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ek.has(o)}let eM=new Set(["table","td","th"]),eN=[":popover-open",":modal"];function eO(e){return eN.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let ej=["transform","translate","scale","rotate","perspective"],eD=["transform","translate","scale","rotate","perspective","filter"],eI=["paint","layout","strict","content"];function eW(e){let t=eF(),n=eA(e)?ez(e):e;return ej.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eD.some(e=>(n.willChange||"").includes(e))||eI.some(e=>(n.contain||"").includes(e))}function eF(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eH=new Set(["html","body","#document"]);function eB(e){return eH.has(eE(e))}function ez(e){return eS(e).getComputedStyle(e)}function e_(e){return eA(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eV(e){if("html"===eE(e))return e;let t=e.assignedSlot||e.parentNode||eL(e)&&e.host||eC(e);return eL(t)?t.host:t}function eG(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eV(t);return eB(n)?t.ownerDocument?t.ownerDocument.body:t.body:eT(n)&&eP(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=eS(o);if(i){let e=eK(l);return t.concat(l,l.visualViewport||[],eP(o)?o:[],e&&n?eG(e):[])}return t.concat(o,eG(o,[],n))}function eK(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eY(e){let t=ez(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eT(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=X(n)!==i||X(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eU(e){return eA(e)?e:e.contextElement}function eX(e){let t=eU(e);if(!eT(t))return q(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eY(t),l=(i?X(n.width):n.width)/r,a=(i?X(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let e$=q(0);function eq(e){let t=eS(e);return eF()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:e$}function eZ(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eU(e),a=q(1);t&&(r?eA(r)&&(a=eX(r)):a=eX(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eS(l))&&o)?eq(l):q(0),s=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=eS(l),t=r&&eA(r)?eS(r):r,n=e,o=eK(n);for(;o&&r&&t!==n;){let e=eX(o),t=o.getBoundingClientRect(),r=ez(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,d*=e.x,f*=e.y,s+=i,c+=l,o=eK(n=eS(o))}}return ep({width:d,height:f,x:s,y:c})}function eJ(e,t){let n=e_(e).scrollLeft;return t?t.left+n:eZ(eC(e)).left+n}function eQ(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eJ(e,r)),y:r.top+t.scrollTop}}let e0=new Set(["absolute","fixed"]);function e1(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eS(e),r=eC(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eF();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eC(e),n=e_(e),r=e.ownerDocument.body,o=U(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=U(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eJ(e),a=-n.scrollTop;return"rtl"===ez(r).direction&&(l+=U(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eC(e));else if(eA(t))r=function(e,t){let n=eZ(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eT(e)?eX(e):q(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eq(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ep(r)}function e2(e){return"static"===ez(e).position}function e5(e,t){if(!eT(e)||"fixed"===ez(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eC(e)===n&&(n=n.ownerDocument.body),n}function e6(e,t){var n;let r=eS(e);if(eO(e))return r;if(!eT(e)){let t=eV(e);for(;t&&!eB(t);){if(eA(t)&&!e2(t))return t;t=eV(t)}return r}let o=e5(e,t);for(;o&&(n=o,eM.has(eE(n)))&&e2(o);)o=e5(o,t);return o&&eB(o)&&e2(o)&&!eW(o)?r:o||function(e){let t=eV(e);for(;eT(t)&&!eB(t);){if(eW(t))return t;if(eO(t))break;t=eV(t)}return null}(e)||r}let e9=async function(e){let t=this.getOffsetParent||e6,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eT(t),o=eC(t),i="fixed"===n,l=eZ(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=q(0);if(r||!r&&!i)if(("body"!==eE(t)||eP(o))&&(a=e_(t)),r){let e=eZ(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eJ(o));i&&!r&&o&&(u.x=eJ(o));let s=!o||r||i?q(0):eQ(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e3={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eC(r),a=!!t&&eO(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=q(1),c=q(0),d=eT(r);if((d||!d&&!i)&&(("body"!==eE(r)||eP(l))&&(u=e_(r)),eT(r))){let e=eZ(r);s=eX(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!l||d||i?q(0):eQ(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+f.x,y:n.y*s.y-u.scrollTop*s.y+c.y+f.y}},getDocumentElement:eC,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eO(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eG(e,[],!1).filter(e=>eA(e)&&"body"!==eE(e)),o=null,i="fixed"===ez(e).position,l=i?eV(e):e;for(;eA(l)&&!eB(l);){let t=ez(l),n=eW(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e0.has(o.position)||eP(l)&&!n&&function e(t,n){let r=eV(t);return!(r===n||!eA(r)||eB(r))&&("fixed"===ez(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eV(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=e1(t,n,o);return e.top=U(r.top,e.top),e.right=Y(r.right,e.right),e.bottom=Y(r.bottom,e.bottom),e.left=U(r.left,e.left),e},e1(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:e6,getElementRects:e9,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eY(e);return{width:t,height:n}},getScale:eX,isElement:eA,isRTL:function(e){return"rtl"===ez(e).direction}};function e7(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e8=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:c=0}=Q(e,t)||{};if(null==s)return{};let d=ef(c),f={x:n,y:r},p=en(ei(o)),h=er(p),v=await l.getDimensions(s),m="y"===p,g=m?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[h]);let E=b/2-v[h]/2-1,S=Y(d[m?"top":"left"],E),C=Y(d[m?"bottom":"right"],E),R=b-v[h]-C,A=b/2-v[h]/2+(y/2-w/2),T=U(S,Y(A,R)),L=!u.arrow&&null!=et(o)&&A!==T&&i.reference[h]/2-(A<S?S:C)-v[h]/2<0,k=L?A<S?A-S:A-R:0;return{[p]:f[p]+k,data:{[p]:T,centerOffset:A-T-k,...L&&{alignmentOffset:k}},reset:L}}});var e4="undefined"!=typeof document?a.useLayoutEffect:function(){};function te(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!te(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!te(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function tt(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tn(e,t){let n=tt(e);return Math.round(t*n)/n}function tr(e){let t=a.useRef(e);return e4(()=>{t.current=e}),t}var to=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,v.jsx)(S.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});to.displayName="Arrow";var ti="Popper",[tl,ta]=m(ti),[tu,ts]=tl(ti),tc=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,v.jsx)(tu,{scope:t,anchor:r,onAnchorChange:o,children:n})};tc.displayName=ti;var td="PopperAnchor",tf=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=ts(td,n),l=a.useRef(null),u=(0,g.s)(t,l);return a.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,v.jsx)(S.sG.div,{...o,ref:u})});tf.displayName=td;var tp="PopperContent",[th,tv]=tl(tp),tm=a.forwardRef((e,t)=>{var n,r,o,i,l,u,c,d;let{__scopePopper:f,side:p="bottom",sideOffset:h=0,align:m="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:T="optimized",onPlaced:L,...k}=e,P=ts(tp,f),[M,N]=a.useState(null),O=(0,g.s)(t,e=>N(e)),[j,D]=a.useState(null),I=function(e){let[t,n]=a.useState(void 0);return z(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),W=null!=(c=null==I?void 0:I.width)?c:0,F=null!=(d=null==I?void 0:I.height)?d:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},B=Array.isArray(b)?b:[b],_=B.length>0,V={padding:H,boundary:B.filter(tx),altBoundary:_},{refs:G,floatingStyles:K,placement:X,isPositioned:q,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:u=!0,whileElementsMounted:c,open:d}=e,[f,p]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,v]=a.useState(r);te(h,r)||v(r);let[m,g]=a.useState(null),[y,w]=a.useState(null),x=a.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=a.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||m,S=l||y,C=a.useRef(null),R=a.useRef(null),A=a.useRef(f),T=null!=c,L=tr(c),k=tr(o),P=tr(d),M=a.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};k.current&&(e.platform=k.current),((e,t,n)=>{let r=new Map,o={platform:e3,...n},i={...o.platform,_c:r};return ev(e,t,{...o,platform:i})})(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};N.current&&!te(A.current,t)&&(A.current=t,s.flushSync(()=>{p(t)}))})},[h,t,n,k,P]);e4(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let N=a.useRef(!1);e4(()=>(N.current=!0,()=>{N.current=!1}),[]),e4(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(L.current)return L.current(E,S,M);M()}},[E,S,M,L,T]);let O=a.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),j=a.useMemo(()=>({reference:E,floating:S}),[E,S]),D=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=tn(j.floating,f.x),r=tn(j.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...tt(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,f.x,f.y]);return a.useMemo(()=>({...f,update:M,refs:O,elements:j,floatingStyles:D}),[f,M,O,j,D])}({strategy:"fixed",placement:p+("center"!==m?"-"+m:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,c=eU(e),d=i||l?[...c?eG(c):[],...eG(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=c&&u?function(e,t){let n,r=null,o=eC(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=s;if(a||t(),!f||!p)return;let h=$(d),v=$(o.clientWidth-(c+f)),m={rootMargin:-h+"px "+-v+"px "+-$(o.clientHeight-(d+p))+"px "+-$(c)+"px",threshold:U(0,Y(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e7(s,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,m)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!s&&h.observe(c),h.observe(t));let v=s?eZ(e):null;return s&&function t(){let r=eZ(e);v&&!e7(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:P.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await ex(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:h+F,alignmentAxis:y}),x&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=Q(e,t),s={x:n,y:r},c=await em(t,u),d=ei(ee(o)),f=en(d),p=s[f],h=s[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=U(n,Y(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=U(n,Y(h,r))}let v=a.fn({...t,[f]:p,[d]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=Q(e,t),c={x:n,y:r},d=ei(o),f=en(d),p=c[f],h=c[d],v=Q(a,t),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var g,y;let e="y"===f?"width":"height",t=ew.has(ee(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}))():void 0,...V}),x&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=Q(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=ee(a),b=ei(c),E=ee(c)===c,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=v||(E||!y?[ed(c)]:function(e){let t=ed(e);return[el(e),t,el(t)]}(c)),R="none"!==g;!v&&R&&C.push(...function(e,t,n,r){let o=et(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?eu:ea;return t?ea:eu;case"left":case"right":return t?es:ec;default:return[]}}(ee(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(el)))),i}(c,y,g,S));let A=[c,...C],T=await em(t,w),L=[],k=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&L.push(T[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=et(e),o=en(ei(e)),i=er(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ed(l)),[l,ed(l)]}(a,s,S);L.push(T[e[0]],T[e[1]])}if(k=[...k,{placement:a,overflows:L}],!L.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||b===ei(t)||k.every(e=>ei(e.placement)!==b||e.overflows[0]>0)))return{data:{index:e,overflows:k},reset:{placement:t}};let n=null==(i=k.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=k.filter(e=>{if(R){let t=ei(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...V}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:s}=t,{apply:c=()=>{},...d}=Q(e,t),f=await em(t,d),p=ee(l),h=et(l),v="y"===ei(l),{width:m,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=m-f.left-f.right,x=Y(g-f[o],y),b=Y(m-f[i],w),E=!t.middlewareData.shift,S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=U(f.left,0),t=U(f.right,0),n=U(f.top,0),r=U(f.bottom,0);v?C=m-2*(0!==e||0!==t?e+t:U(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:U(f.top,f.bottom))}await c({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(s.floating);return m!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e8({element:n.current,padding:r}).fn(t):{}:n?e8({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:j,padding:w}),tb({arrowWidth:W,arrowHeight:F}),A&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=Q(e,t);switch(r){case"referenceHidden":{let e=eg(await em(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ey(e)}}}case"escaped":{let e=eg(await em(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ey(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...V})]}),[J,eo]=tE(X),ef=C(L);z(()=>{q&&(null==ef||ef())},[q,ef]);let ep=null==(n=Z.arrow)?void 0:n.x,eh=null==(r=Z.arrow)?void 0:r.y,eb=(null==(o=Z.arrow)?void 0:o.centerOffset)!==0,[eE,eS]=a.useState();return z(()=>{M&&eS(window.getComputedStyle(M).zIndex)},[M]),(0,v.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:q?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eE,"--radix-popper-transform-origin":[null==(i=Z.transformOrigin)?void 0:i.x,null==(l=Z.transformOrigin)?void 0:l.y].join(" "),...(null==(u=Z.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(th,{scope:f,placedSide:J,onArrowChange:D,arrowX:ep,arrowY:eh,shouldHideArrow:eb,children:(0,v.jsx)(S.sG.div,{"data-side":J,"data-align":eo,...k,ref:O,style:{...k.style,animation:q?void 0:"none"}})})})});tm.displayName=tp;var tg="PopperArrow",ty={top:"bottom",right:"left",bottom:"top",left:"right"},tw=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tv(tg,n),i=ty[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(to,{...r,ref:t,style:{...r.style,display:"block"}})})});function tx(e){return null!==e}tw.displayName=tg;var tb=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=tE(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=c?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=c?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=c?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function tE(e){let[t,n="center"]=e.split("-");return[t,n]}var tS=a.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,u]=a.useState(!1);z(()=>u(!0),[]);let c=o||l&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?s.createPortal((0,v.jsx)(S.sG.div,{...i,ref:t}),c):null});tS.displayName="Portal";var tC=u[" useInsertionEffect ".trim().toString()]||z;function tR({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,l]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return tC(()=>{i.current=t},[t]),a.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else i(t)},[u,e,i,l])]}Symbol("RADIX:SYNC_STATE");var tA=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});a.forwardRef((e,t)=>(0,v.jsx)(S.sG.span,{...e,ref:t,style:{...tA,...e.style}})).displayName="VisuallyHidden";var tT=new WeakMap,tL=new WeakMap,tk={},tP=0,tM=function(e){return e&&(e.host||tM(e.parentNode))},tN=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tM(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tk[n]||(tk[n]=new WeakMap);var i=tk[n],l=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tT.get(e)||0)+1,s=(i.get(e)||0)+1;tT.set(e,u),i.set(e,s),l.push(e),1===u&&o&&tL.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),tP++,function(){l.forEach(function(e){var t=tT.get(e)-1,o=i.get(e)-1;tT.set(e,t),i.set(e,o),t||(tL.has(e)||e.removeAttribute(r),tL.delete(e)),o||e.removeAttribute(n)}),--tP||(tT=new WeakMap,tT=new WeakMap,tL=new WeakMap,tk={})}},tO=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tN(r,o,n,"aria-hidden")):function(){return null}},tj=function(){return(tj=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tD(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tI=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tW="width-before-scroll-bar";function tF(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tH="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,tB=new WeakMap;function tz(e){return e}var t_=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=tz),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=tj({async:!0,ssr:!1},e),o}(),tV=function(){},tG=a.forwardRef(function(e,t){var n,r,o,i,l=a.useRef(null),u=a.useState({onScrollCapture:tV,onWheelCapture:tV,onTouchMoveCapture:tV}),s=u[0],c=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tD(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[l,t],r=function(e){return n.forEach(function(t){return tF(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tH(function(){var e=tB.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tF(e,null)}),r.forEach(function(e){t.has(e)||tF(e,o)})}tB.set(i,n)},[n]),i),A=tj(tj({},C),s);return a.createElement(a.Fragment,null,v&&a.createElement(g,{sideCar:t_,removeScrollBar:h,shards:m,noRelative:y,noIsolation:w,inert:x,setCallbacks:c,allowPinchZoom:!!b,lockRef:l,gapMode:S}),d?a.cloneElement(a.Children.only(f),tj(tj({},A),{ref:R})):a.createElement(void 0===E?"div":E,tj({},A,{className:p,ref:R}),f))});tG.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tG.classNames={fullWidth:tW,zeroRight:tI};var tK=function(e){var t=e.sideCar,n=tD(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,tj({},n))};tK.isSideCarExport=!0;var tY=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tU=function(){var e=tY();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tX=function(){var e=tU();return function(t){return e(t.styles,t.dynamic),null}},t$={left:0,top:0,right:0,gap:0},tq=function(e){return parseInt(e||"",10)||0},tZ=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tq(n),tq(r),tq(o)]},tJ=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t$;var t=tZ(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tQ=tX(),t0="data-scroll-locked",t1=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t0,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tI," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tW," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tI," .").concat(tI," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tW," .").concat(tW," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t0,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t2=function(){var e=parseInt(document.body.getAttribute(t0)||"0",10);return isFinite(e)?e:0},t5=function(){a.useEffect(function(){return document.body.setAttribute(t0,(t2()+1).toString()),function(){var e=t2()-1;e<=0?document.body.removeAttribute(t0):document.body.setAttribute(t0,e.toString())}},[])},t6=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t5();var i=a.useMemo(function(){return tJ(o)},[o]);return a.createElement(tQ,{styles:t1(i,!t,o,n?"":"!important")})},t9=!1;if("undefined"!=typeof window)try{var t3=Object.defineProperty({},"passive",{get:function(){return t9=!0,!0}});window.addEventListener("test",t3,t3),window.removeEventListener("test",t3,t3)}catch(e){t9=!1}var t7=!!t9&&{passive:!1},t8=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},t4=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ne(e,r)){var o=nt(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ne=function(e,t){return"v"===e?t8(t,"overflowY"):t8(t,"overflowX")},nt=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nn=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=nt(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&ne(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},nr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},no=function(e){return[e.deltaX,e.deltaY]},ni=function(e){return e&&"current"in e?e.current:e},nl=0,na=[];let nu=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(nl++)[0],i=a.useState(tX)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ni),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nr(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=t4(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t4(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return nn(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(na.length&&na[na.length-1]===i){var n="deltaY"in e?no(e):nr(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(ni).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=nr(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,no(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,nr(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return na.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,t7),document.addEventListener("touchmove",s,t7),document.addEventListener("touchstart",d,t7),function(){na=na.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,t7),document.removeEventListener("touchmove",s,t7),document.removeEventListener("touchstart",d,t7)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(t6,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t_.useMedium(r),tK);var ns=a.forwardRef(function(e,t){return a.createElement(tG,tj({},e,{ref:t,sideCar:nu}))});ns.classNames=tG.classNames;var nc=[" ","Enter","ArrowUp","ArrowDown"],nd=[" ","Enter"],nf="Select",[np,nh,nv]=function(e){let t=e+"CollectionProvider",[n,r]=m(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,v.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let u=e+"CollectionSlot",s=(0,y.TL)(u),c=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),l=(0,g.s)(t,o.collectionRef);return(0,v.jsx)(s,{ref:l,children:r})});c.displayName=u;let d=e+"CollectionItemSlot",f="data-radix-collection-item",p=(0,y.TL)(d),h=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,g.s)(t,l),s=i(d,n);return a.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,v.jsx)(p,{...{[f]:""},ref:u,children:r})});return h.displayName=d,[{Provider:l,Slot:c,ItemSlot:h},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(f,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(nf),[nm,ng]=m(nf,[nv,ta]),ny=ta(),[nw,nx]=nm(nf),[nb,nE]=nm(nf),nS=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:u,onValueChange:s,dir:c,name:d,autoComplete:f,disabled:p,required:h,form:m}=e,g=ny(t),[y,w]=a.useState(null),[x,b]=a.useState(null),[S,C]=a.useState(!1),R=function(e){let t=a.useContext(E);return e||t||"ltr"}(c),[A,T]=tR({prop:r,defaultProp:null!=o&&o,onChange:i,caller:nf}),[L,k]=tR({prop:l,defaultProp:u,onChange:s,caller:nf}),P=a.useRef(null),M=!y||m||!!y.closest("form"),[N,O]=a.useState(new Set),j=Array.from(N).map(e=>e.props.value).join(";");return(0,v.jsx)(tc,{...g,children:(0,v.jsxs)(nw,{required:h,scope:t,trigger:y,onTriggerChange:w,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:G(),value:L,onValueChange:k,open:A,onOpenChange:T,dir:R,triggerPointerDownPosRef:P,disabled:p,children:[(0,v.jsx)(np.Provider,{scope:t,children:(0,v.jsx)(nb,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:a.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,v.jsxs)(n4,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:L,onChange:e=>k(e.target.value),disabled:p,form:m,children:[void 0===L?(0,v.jsx)("option",{value:""}):null,Array.from(N)]},j):null]})})};nS.displayName=nf;var nC="SelectTrigger",nR=a.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=ny(n),l=nx(nC,n),u=l.disabled||r,s=(0,g.s)(t,l.onTriggerChange),c=nh(n),f=a.useRef("touch"),[p,h,m]=rt(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=rn(t,e,n);void 0!==r&&l.onValueChange(r.value)}),y=e=>{u||(l.onOpenChange(!0),m()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(tf,{asChild:!0,...i,children:(0,v.jsx)(S.sG.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":re(l.value)?"":void 0,...o,ref:s,onClick:d(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:d(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:d(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&nc.includes(e.key)&&(y(),e.preventDefault())})})})});nR.displayName=nC;var nA="SelectValue",nT=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nx(nA,n),{onValueNodeHasChildrenChange:s}=u,c=void 0!==i,d=(0,g.s)(t,u.onValueNodeChange);return z(()=>{s(c)},[s,c]),(0,v.jsx)(S.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:re(u.value)?(0,v.jsx)(v.Fragment,{children:l}):i})});nT.displayName=nA;var nL=a.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,v.jsx)(S.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nL.displayName="SelectIcon";var nk=e=>(0,v.jsx)(tS,{asChild:!0,...e});nk.displayName="SelectPortal";var nP="SelectContent",nM=a.forwardRef((e,t)=>{let n=nx(nP,e.__scopeSelect),[r,o]=a.useState();return(z(()=>{o(new DocumentFragment)},[]),n.open)?(0,v.jsx)(nD,{...e,ref:t}):r?s.createPortal((0,v.jsx)(nN,{scope:e.__scopeSelect,children:(0,v.jsx)(np.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),r):null});nM.displayName=nP;var[nN,nO]=nm(nP),nj=(0,y.TL)("SelectContent.RemoveScroll"),nD=a.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:u,sideOffset:s,align:c,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:w,avoidCollisions:x,...b}=e,E=nx(nP,n),[S,C]=a.useState(null),[R,A]=a.useState(null),L=(0,g.s)(t,e=>C(e)),[k,N]=a.useState(null),[O,j]=a.useState(null),I=nh(n),[W,F]=a.useState(!1),H=a.useRef(!1);a.useEffect(()=>{if(S)return tO(S)},[S]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:M()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:M()),P++,()=>{1===P&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),P--}},[]);let B=a.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[I,R]),z=a.useCallback(()=>B([k,S]),[B,k,S]);a.useEffect(()=>{W&&z()},[W,z]);let{onOpenChange:_,triggerPointerDownPosRef:V}=E;a.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=V.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=V.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,_,V]),a.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[G,K]=rt(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=rn(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Y=a.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&(N(e),r&&(H.current=!0))},[E.value]),U=a.useCallback(()=>null==S?void 0:S.focus(),[S]),X=a.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==E.value&&E.value===t||r)&&j(e)},[E.value]),$="popper"===r?nW:nI,q=$===nW?{side:u,sideOffset:s,align:c,alignOffset:f,arrowPadding:p,collisionBoundary:h,collisionPadding:m,sticky:y,hideWhenDetached:w,avoidCollisions:x}:{};return(0,v.jsx)(nN,{scope:n,content:S,viewport:R,onViewportChange:A,itemRefCallback:Y,selectedItem:k,onItemLeave:U,itemTextRefCallback:X,focusSelectedItem:z,selectedItemText:O,position:r,isPositioned:W,searchRef:G,children:(0,v.jsx)(ns,{as:nj,allowPinchZoom:!0,children:(0,v.jsx)(D,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:d(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(T,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,v.jsx)($,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...q,onPlaced:()=>F(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:d(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});nD.displayName="SelectContentImpl";var nI=a.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nx(nP,n),l=nO(nP,n),[u,s]=a.useState(null),[d,f]=a.useState(null),p=(0,g.s)(t,e=>f(e)),h=nh(n),m=a.useRef(!1),y=a.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:b,focusSelectedItem:E}=l,C=a.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&d&&w&&x&&b){let e=i.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,s=Math.max(a,t.width),d=c(i,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,s=Math.max(a,t.width),d=c(i,[10,Math.max(10,window.innerWidth-10-s)]);u.style.minWidth=a+"px",u.style.right=d+"px"}let l=h(),a=window.innerHeight-20,s=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),y=p+v+s+parseInt(f.paddingBottom,10)+g,E=Math.min(5*x.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=x.offsetHeight/2,L=p+v+(x.offsetTop+T);if(L<=A){let e=l.length>0&&x===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(a-A,T+(e?R:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+g);u.style.height=L+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;u.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?C:0)+T);u.style.height=t+(y-L)+"px",w.scrollTop=L-A+w.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=E+"px",u.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,d,w,x,b,i.dir,r]);z(()=>C(),[C]);let[R,A]=a.useState();z(()=>{d&&A(window.getComputedStyle(d).zIndex)},[d]);let T=a.useCallback(e=>{e&&!0===y.current&&(C(),null==E||E(),y.current=!1)},[C,E]);return(0,v.jsx)(nF,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,v.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,v.jsx)(S.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nI.displayName="SelectItemAlignedPosition";var nW=a.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ny(n);return(0,v.jsx)(tm,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nW.displayName="SelectPopperPosition";var[nF,nH]=nm(nP,{}),nB="SelectViewport",nz=a.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nO(nB,n),l=nH(nB,n),u=(0,g.s)(t,i.onViewportChange),s=a.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,v.jsx)(np.Slot,{scope:n,children:(0,v.jsx)(S.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:d(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if((null==r?void 0:r.current)&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});nz.displayName=nB;var n_="SelectGroup",[nV,nG]=nm(n_),nK=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=G();return(0,v.jsx)(nV,{scope:n,id:o,children:(0,v.jsx)(S.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nK.displayName=n_;var nY="SelectLabel",nU=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nG(nY,n);return(0,v.jsx)(S.sG.div,{id:o.id,...r,ref:t})});nU.displayName=nY;var nX="SelectItem",[n$,nq]=nm(nX),nZ=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,u=nx(nX,n),s=nO(nX,n),c=u.value===r,[f,p]=a.useState(null!=i?i:""),[h,m]=a.useState(!1),y=(0,g.s)(t,e=>{var t;return null==(t=s.itemRefCallback)?void 0:t.call(s,e,r,o)}),w=G(),x=a.useRef("touch"),b=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(n$,{scope:n,value:r,disabled:o,textId:w,isSelected:c,onItemTextChange:a.useCallback(e=>{p(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,v.jsx)(np.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,v.jsx)(S.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":h?"":void 0,"aria-selected":c&&h,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:y,onFocus:d(l.onFocus,()=>m(!0)),onBlur:d(l.onBlur,()=>m(!1)),onClick:d(l.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:d(l.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:d(l.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:d(l.onPointerMove,e=>{if(x.current=e.pointerType,o){var t;null==(t=s.onItemLeave)||t.call(s)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:d(l.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=s.onItemLeave)||t.call(s)}}),onKeyDown:d(l.onKeyDown,e=>{var t;((null==(t=s.searchRef)?void 0:t.current)===""||" "!==e.key)&&(nd.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});nZ.displayName=nX;var nJ="SelectItemText",nQ=a.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nx(nJ,n),u=nO(nJ,n),c=nq(nJ,n),d=nE(nJ,n),[f,p]=a.useState(null),h=(0,g.s)(t,e=>p(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,y=a.useMemo(()=>(0,v.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return z(()=>(w(y),()=>x(y)),[w,x,y]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(S.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&l.valueNode&&!l.valueNodeHasChildren?s.createPortal(i.children,l.valueNode):null]})});nQ.displayName=nJ;var n0="SelectItemIndicator",n1=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nq(n0,n).isSelected?(0,v.jsx)(S.sG.span,{"aria-hidden":!0,...r,ref:t}):null});n1.displayName=n0;var n2="SelectScrollUpButton",n5=a.forwardRef((e,t)=>{let n=nO(n2,e.__scopeSelect),r=nH(n2,e.__scopeSelect),[o,i]=a.useState(!1),l=(0,g.s)(t,r.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(n3,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n5.displayName=n2;var n6="SelectScrollDownButton",n9=a.forwardRef((e,t)=>{let n=nO(n6,e.__scopeSelect),r=nH(n6,e.__scopeSelect),[o,i]=a.useState(!1),l=(0,g.s)(t,r.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(n3,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});n9.displayName=n6;var n3=a.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nO("SelectScrollButton",n),l=a.useRef(null),u=nh(n),s=a.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return a.useEffect(()=>()=>s(),[s]),z(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,v.jsx)(S.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:d(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:d(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:d(o.onPointerLeave,()=>{s()})})}),n7=a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,v.jsx)(S.sG.div,{"aria-hidden":!0,...r,ref:t})});n7.displayName="SelectSeparator";var n8="SelectArrow";a.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ny(n),i=nx(n8,n),l=nO(n8,n);return i.open&&"popper"===l.position?(0,v.jsx)(tw,{...o,...r,ref:t}):null}).displayName=n8;var n4=a.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,i=a.useRef(null),l=(0,g.s)(t,i),u=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return a.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,v.jsx)(S.sG.select,{...o,style:{...tA,...o.style},ref:l,defaultValue:r})});function re(e){return""===e||void 0===e}function rt(e){let t=C(e),n=a.useRef(""),r=a.useRef(0),o=a.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=a.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function rn(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}n4.displayName="SelectBubbleInput";var rr=nS,ro=nR,ri=nT,rl=nL,ra=nk,ru=nM,rs=nz,rc=nK,rd=nU,rf=nZ,rp=nQ,rh=n1,rv=n5,rm=n9,rg=n7},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);