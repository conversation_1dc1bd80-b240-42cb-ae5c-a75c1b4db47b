import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Palette, 
  Truck, 
  Users, 
  Scissors, 
  Shield, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Package,
  Ruler,
  Sparkles
} from "lucide-react"
import Link from "next/link"

export default function ServicesPage() {
  const services = [
    {
      icon: Palette,
      title: "Custom Design & Branding",
      description: "Create unique uniforms that perfectly represent your brand identity",
      features: [
        "Logo embroidery and printing",
        "Custom color schemes",
        "Brand guideline compliance",
        "Design consultation"
      ],
      badge: "Popular"
    },
    {
      icon: Scissors,
      title: "Tailoring & Alterations",
      description: "Perfect fit guaranteed with our professional tailoring services",
      features: [
        "Professional measurements",
        "Custom sizing",
        "Alterations and adjustments",
        "Fit consultations"
      ]
    },
    {
      icon: Package,
      title: "Bulk Orders & Corporate Solutions",
      description: "Streamlined ordering process for large organizations",
      features: [
        "Volume discounts",
        "Dedicated account management",
        "Flexible payment terms",
        "Inventory management"
      ],
      badge: "Enterprise"
    },
    {
      icon: Truck,
      title: "Fast Delivery & Logistics",
      description: "Reliable delivery services to get your uniforms when you need them",
      features: [
        "Express delivery options",
        "Nationwide shipping",
        "Order tracking",
        "Scheduled deliveries"
      ]
    },
    {
      icon: Users,
      title: "Consultation & Support",
      description: "Expert guidance throughout your uniform journey",
      features: [
        "Industry expertise",
        "Uniform policy development",
        "Ongoing support",
        "Training and guidelines"
      ]
    },
    {
      icon: Shield,
      title: "Quality Assurance",
      description: "Rigorous quality control ensures every uniform meets our high standards",
      features: [
        "Material testing",
        "Quality inspections",
        "Durability guarantees",
        "Satisfaction warranty"
      ]
    }
  ]

  const process = [
    {
      step: "01",
      title: "Consultation",
      description: "We discuss your needs, requirements, and brand guidelines"
    },
    {
      step: "02", 
      title: "Design & Quote",
      description: "Our team creates custom designs and provides detailed pricing"
    },
    {
      step: "03",
      title: "Sampling",
      description: "Review and approve samples before full production begins"
    },
    {
      step: "04",
      title: "Production",
      description: "Your uniforms are manufactured with strict quality control"
    },
    {
      step: "05",
      title: "Delivery",
      description: "Fast, reliable delivery to your specified location"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold mb-4">Our Services</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive uniform solutions designed to meet your business needs. 
            From custom design to delivery, we handle every aspect of your uniform requirements.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <Card key={index} className="relative">
              {service.badge && (
                <Badge className="absolute -top-2 -right-2 z-10" variant="default">
                  {service.badge}
                </Badge>
              )}
              <CardHeader>
                <service.icon className="h-12 w-12 text-primary mb-4" />
                <CardTitle className="text-xl">{service.title}</CardTitle>
                <CardDescription>{service.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Process Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Process</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              A streamlined approach to delivering the perfect uniform solution
            </p>
          </div>
          
          <div className="grid md:grid-cols-5 gap-6">
            {process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-4">
                  <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto">
                    {step.step}
                  </div>
                  {index < process.length - 1 && (
                    <ArrowRight className="hidden md:block absolute top-1/2 -right-8 transform -translate-y-1/2 text-muted-foreground h-6 w-6" />
                  )}
                </div>
                <h3 className="font-semibold mb-2">{step.title}</h3>
                <p className="text-sm text-muted-foreground">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Specializations */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Industry Specializations</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Tailored solutions for specific industry requirements
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">🏥</div>
                <h3 className="font-semibold mb-2">Healthcare</h3>
                <p className="text-sm text-muted-foreground">
                  Antimicrobial fabrics, comfort features, and professional styling
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">🏨</div>
                <h3 className="font-semibold mb-2">Hospitality</h3>
                <p className="text-sm text-muted-foreground">
                  Elegant designs that enhance guest experience and brand image
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">🛡️</div>
                <h3 className="font-semibold mb-2">Security</h3>
                <p className="text-sm text-muted-foreground">
                  Durable, functional designs with authority and professionalism
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="text-4xl mb-4">🏭</div>
                <h3 className="font-semibold mb-2">Industrial</h3>
                <p className="text-sm text-muted-foreground">
                  Safety-compliant workwear with enhanced durability features
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Additional Services */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <Ruler className="h-8 w-8 text-primary mb-2" />
              <CardTitle>Size Guide & Fitting</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Comprehensive sizing guides and professional fitting services to ensure perfect comfort and appearance.
              </p>
              <Button variant="outline" size="sm">
                View Size Guide
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Sparkles className="h-8 w-8 text-primary mb-2" />
              <CardTitle>Care & Maintenance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Detailed care instructions and maintenance tips to extend the life of your uniforms.
              </p>
              <Button variant="outline" size="sm">
                Care Instructions
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Clock className="h-8 w-8 text-primary mb-2" />
              <CardTitle>Rush Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Need uniforms quickly? Our rush order service can deliver in as little as 48 hours.
              </p>
              <Button variant="outline" size="sm">
                Learn More
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <Card className="bg-primary text-primary-foreground text-center">
          <CardContent className="pt-8 pb-8">
            <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-primary-foreground/80 mb-6 max-w-2xl mx-auto">
              Contact our team today to discuss your uniform requirements and receive a custom quote.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" size="lg" asChild>
                <Link href="/contact">Get Free Quote</Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
