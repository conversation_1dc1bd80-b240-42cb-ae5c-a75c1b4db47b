interface PlaceholderImageProps {
  width?: number
  height?: number
  text?: string
  className?: string
}

export function PlaceholderImage({ 
  width = 400, 
  height = 300, 
  text = "Image", 
  className = "" 
}: PlaceholderImageProps) {
  return (
    <div 
      className={`bg-muted flex items-center justify-center text-muted-foreground ${className}`}
      style={{ width, height }}
    >
      <span className="text-sm font-medium">{text}</span>
    </div>
  )
}
