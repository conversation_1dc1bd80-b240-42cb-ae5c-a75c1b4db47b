{"mode": "dark", "local_flutter_path": "/Users/<USER>/flutter/flutter/bin", "deviceEnvState": {"dart_installed": true, "flutter_installed": true, "flutter_version": "3.27.0", "dart_version": "3.6.0", "flutter_doctor": [{"head": null, "body": "[!] Flutter (Channel stable, 3.27.0, on macOS 15.2 24C101 darwin-arm64, locale en-AE)"}, {"head": null, "body": "    ! The flutter binary is not on your path. Consider adding /Users/<USER>/flutter/flutter/bin to your path."}, {"head": null, "body": "    ! The dart binary is not on your path. Consider adding /Users/<USER>/flutter/flutter/bin to your path."}, {"head": true, "body": "[✓] Android toolchain - develop for Android devices (Android SDK version 35.0.0)"}, {"head": null, "body": "[!] Xcode - develop for iOS and macOS (Xcode 16.2)"}, {"head": false, "body": "    ✗ CocoaPods not installed."}, {"head": true, "body": "[✓] Chrome - develop for the web"}, {"head": true, "body": "[✓] Android Studio (version 2024.2)"}, {"head": true, "body": "[✓] VS Code (version 1.96.0)"}, {"head": true, "body": "[✓] Connected device (5 available)"}, {"head": true, "body": "[✓] Network resources"}, {"head": null, "body": "! Doctor found issues in 2 categories."}]}, "activeProject": {"id": "2ef9455d-76a6-4ffd-ba21-4fcd0e2e61a8", "team_id": "923c1e0f-11f6-4156-8af0-f5d7df2914a9", "client_id": null, "created_at": "2024-12-14T15:40:54.973559+00:00", "title": "HRM", "description": "Human Resource Management", "status": null, "due_date": null, "budget": null, "color_1": "#f44336", "icon": "fi-br-bread-slice.svg", "color_2": "#2196f3", "updated_at": null, "admin_id": "2d1c5325-72ae-4d1f-a033-d8d0c0b387d9", "state_management": "bloc"}, "projects_local_path": [{"id": "2ef9455d-76a6-4ffd-ba21-4fcd0e2e61a8", "path": "/Users/<USER>/Projects/h_r_m"}]}