import { AIProcessingResult, Flashcard, Quiz, QuizQuestion } from '@/types';

// Mock AI service for demonstration - in production, you'd use OpenAI, Claude, or similar
export class AIService {
  private static instance: AIService;

  private constructor() {}

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  async processText(text: string, subject?: string): Promise<AIProcessingResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock AI processing - in production, this would call actual AI APIs
    const summary = this.generateSummary(text);
    const keyPoints = this.extractKeyPoints(text);
    const suggestedTags = this.generateTags(text, subject);
    const flashcards = this.generateFlashcards(text);
    const quiz = this.generateQuiz(text);

    return {
      summary,
      keyPoints,
      suggestedTags,
      flashcards,
      quiz,
    };
  }

  private generateSummary(text: string): string {
    // Simple extractive summarization - take first few sentences
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const summaryLength = Math.min(3, Math.ceil(sentences.length * 0.3));
    
    return sentences.slice(0, summaryLength).join('. ') + '.';
  }

  private extractKeyPoints(text: string): string[] {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const keyPoints: string[] = [];

    // Look for sentences with key indicators
    const keyIndicators = [
      'important', 'key', 'main', 'primary', 'essential', 'crucial',
      'remember', 'note', 'definition', 'formula', 'theorem', 'law'
    ];

    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      if (keyIndicators.some(indicator => lowerSentence.includes(indicator))) {
        keyPoints.push(sentence.trim());
      }
    });

    // If no key indicators found, take every 3rd sentence
    if (keyPoints.length === 0) {
      for (let i = 0; i < sentences.length; i += 3) {
        keyPoints.push(sentences[i].trim());
      }
    }

    return keyPoints.slice(0, 5); // Limit to 5 key points
  }

  private generateTags(text: string, subject?: string): string[] {
    const tags: string[] = [];
    
    if (subject) {
      tags.push(subject);
    }

    // Common academic terms
    const academicTerms = [
      'mathematics', 'algebra', 'calculus', 'geometry',
      'physics', 'chemistry', 'biology', 'science',
      'history', 'literature', 'english', 'writing',
      'computer', 'programming', 'algorithm', 'data',
      'economics', 'business', 'finance', 'marketing'
    ];

    const lowerText = text.toLowerCase();
    academicTerms.forEach(term => {
      if (lowerText.includes(term) && !tags.includes(term)) {
        tags.push(term);
      }
    });

    // Extract potential topics (capitalized words)
    const words = text.match(/\b[A-Z][a-z]+\b/g) || [];
    const uniqueWords = [...new Set(words)];
    
    uniqueWords.slice(0, 3).forEach(word => {
      if (!tags.includes(word.toLowerCase())) {
        tags.push(word.toLowerCase());
      }
    });

    return tags.slice(0, 6);
  }

  private generateFlashcards(text: string): Omit<Flashcard, 'id' | 'noteId' | 'createdAt' | 'userId' | 'reviewCount' | 'lastReviewed' | 'nextReview'>[] {
    const flashcards: Omit<Flashcard, 'id' | 'noteId' | 'createdAt' | 'userId' | 'reviewCount' | 'lastReviewed' | 'nextReview'>[] = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);

    // Look for definition patterns
    const definitionPatterns = [
      /(.+?)\s+is\s+(.+)/i,
      /(.+?)\s+are\s+(.+)/i,
      /(.+?):\s*(.+)/,
      /(.+?)\s+means\s+(.+)/i,
      /(.+?)\s+refers to\s+(.+)/i
    ];

    sentences.forEach(sentence => {
      for (const pattern of definitionPatterns) {
        const match = sentence.match(pattern);
        if (match && match[1] && match[2]) {
          const question = `What is ${match[1].trim()}?`;
          const answer = match[2].trim();
          
          flashcards.push({
            question,
            answer,
            difficulty: 'medium',
          });
          
          if (flashcards.length >= 8) return;
        }
      }
    });

    // If no definitions found, create Q&A from key sentences
    if (flashcards.length === 0) {
      const keyWords = text.match(/\b[A-Z][a-z]+\b/g) || [];
      const uniqueWords = [...new Set(keyWords)].slice(0, 5);

      uniqueWords.forEach(word => {
        const wordSentences = sentences.filter(s => s.includes(word));
        if (wordSentences.length > 0) {
          flashcards.push({
            question: `What do you know about ${word}?`,
            answer: wordSentences[0].trim(),
            difficulty: 'medium',
          });
        }
      });
    }

    return flashcards.slice(0, 8);
  }

  private generateQuiz(text: string): Omit<Quiz, 'id' | 'noteId' | 'createdAt' | 'userId' | 'score' | 'completedAt'> {
    const questions: QuizQuestion[] = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);

    // Generate multiple choice questions
    sentences.slice(0, 5).forEach((sentence, index) => {
      const words = sentence.split(' ').filter(w => w.length > 3);
      if (words.length > 5) {
        const keyWord = words[Math.floor(words.length / 2)];
        const questionText = sentence.replace(keyWord, '______');
        
        const options = [
          keyWord,
          this.generateDistractor(keyWord),
          this.generateDistractor(keyWord),
          this.generateDistractor(keyWord)
        ].sort(() => Math.random() - 0.5);

        questions.push({
          id: `q${index + 1}`,
          type: 'multiple-choice',
          question: `Fill in the blank: ${questionText}`,
          options,
          correctAnswer: keyWord,
          explanation: `The correct answer is "${keyWord}" as mentioned in the original text.`
        });
      }
    });

    // Generate true/false questions
    if (sentences.length > 2) {
      const randomSentence = sentences[Math.floor(Math.random() * sentences.length)];
      questions.push({
        id: `q${questions.length + 1}`,
        type: 'true-false',
        question: `True or False: ${randomSentence}`,
        correctAnswer: 'True',
        explanation: 'This statement appears in the original text.'
      });
    }

    return {
      title: 'Auto-generated Quiz',
      questions: questions.slice(0, 5)
    };
  }

  private generateDistractor(word: string): string {
    const distractors = [
      'concept', 'element', 'factor', 'aspect', 'component',
      'principle', 'method', 'process', 'system', 'structure',
      'function', 'property', 'characteristic', 'feature', 'attribute'
    ];
    
    return distractors[Math.floor(Math.random() * distractors.length)];
  }

  async enhanceText(text: string): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simple text enhancement - fix common issues
    let enhanced = text
      .replace(/\s+/g, ' ') // Multiple spaces to single space
      .replace(/([.!?])\s*([a-z])/g, '$1 $2') // Fix spacing after punctuation
      .replace(/([a-z])([A-Z])/g, '$1 $2') // Add space between lowercase and uppercase
      .trim();

    // Capitalize first letter of sentences
    enhanced = enhanced.replace(/(^|[.!?]\s+)([a-z])/g, (match, p1, p2) => p1 + p2.toUpperCase());

    return enhanced;
  }
}

// Export singleton instance
export const aiService = AIService.getInstance();
