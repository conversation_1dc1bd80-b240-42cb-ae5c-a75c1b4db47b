// Core types for SnapStudy application

export interface Note {
  id: string;
  title: string;
  content: string;
  extractedText: string;
  imageUrl?: string;
  subject?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface Flashcard {
  id: string;
  noteId: string;
  question: string;
  answer: string;
  difficulty: 'easy' | 'medium' | 'hard';
  reviewCount: number;
  lastReviewed?: Date;
  nextReview?: Date;
  createdAt: Date;
  userId: string;
}

export interface Quiz {
  id: string;
  noteId: string;
  title: string;
  questions: QuizQuestion[];
  score?: number;
  completedAt?: Date;
  createdAt: Date;
  userId: string;
}

export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'fill-blank';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation?: string;
  userAnswer?: string;
  isCorrect?: boolean;
}

export interface StudySession {
  id: string;
  userId: string;
  type: 'flashcards' | 'quiz';
  noteIds: string[];
  startedAt: Date;
  completedAt?: Date;
  score?: number;
  totalQuestions: number;
  correctAnswers: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  plan: 'free' | 'pro';
  createdAt: Date;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultSubject?: string;
  reminderEnabled: boolean;
  studyGoal: number; // minutes per day
}

export interface OCRResult {
  text: string;
  confidence: number;
  boundingBoxes?: BoundingBox[];
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
}

export interface AIProcessingResult {
  summary: string;
  keyPoints: string[];
  suggestedTags: string[];
  flashcards: Omit<Flashcard, 'id' | 'noteId' | 'createdAt' | 'userId' | 'reviewCount' | 'lastReviewed' | 'nextReview'>[];
  quiz: Omit<Quiz, 'id' | 'noteId' | 'createdAt' | 'userId' | 'score' | 'completedAt'>;
}
