"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Check,
  Download,
  Menu,
  Upload,
  X,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* Header */}
      <header className="container mx-auto px-6 py-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Camera className="h-8 w-8 text-blue-400" />
            <h1 className="text-2xl font-bold text-white">SnapStudy</h1>
          </div>
          <nav className="hidden md:flex space-x-6">
            <a
              href="#features"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Features
            </a>
            <a
              href="#how-it-works"
              className="text-gray-300 hover:text-white transition-colors"
            >
              How it Works
            </a>
            <a
              href="#pricing"
              className="text-gray-300 hover:text-white transition-colors"
            >
              Pricing
            </a>
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-gray-300 hover:text-white transition-colors"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-gray-800 pt-4">
            <div className="flex flex-col space-y-2">
              <a
                href="#features"
                className="text-gray-300 hover:text-white transition-colors py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Features
              </a>
              <a
                href="#how-it-works"
                className="text-gray-300 hover:text-white transition-colors py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                How it Works
              </a>
              <a
                href="#pricing"
                className="text-gray-300 hover:text-white transition-colors py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Pricing
              </a>
            </div>
          </nav>
        )}
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-6 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Learn from Your Notes
            <span className="text-blue-400"> Instantly</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Transform your handwritten or printed notes into interactive study
            materials with AI-powered digitization, summarization, and flashcard
            generation.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Link
              href="/dashboard"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2"
            >
              <Camera className="h-5 w-5" />
              <span>Start Scanning Notes</span>
            </Link>
            <button className="border border-gray-600 hover:border-gray-500 text-white px-8 py-4 rounded-lg font-semibold transition-colors">
              Watch Demo
            </button>
          </div>

          {/* Feature Cards */}
          <div id="features" className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:bg-gray-800/70 hover:border-blue-500/50 hover:scale-105 transform transition-all duration-300 cursor-pointer group">
              <Camera className="h-12 w-12 text-blue-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-300 transition-colors duration-300">
                OCR Scanner
              </h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                Take photos of handwritten or printed notes and instantly
                digitize them with advanced OCR technology.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:bg-gray-800/70 hover:border-purple-500/50 hover:scale-105 transform transition-all duration-300 cursor-pointer group">
              <Brain className="h-12 w-12 text-purple-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors duration-300">
                AI Summarizer
              </h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                Get key points and simplified explanations from your notes using
                advanced AI processing.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:bg-gray-800/70 hover:border-green-500/50 hover:scale-105 transform transition-all duration-300 cursor-pointer group">
              <BookOpen className="h-12 w-12 text-green-400 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
              <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-green-300 transition-colors duration-300">
                Smart Flashcards
              </h3>
              <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                Automatically generate flashcards and quizzes from your content
                for effective studying.
              </p>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <section id="how-it-works" className="mt-32">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">How It Works</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Transform your handwritten notes into interactive study materials
              in just three simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center">
              <div className="bg-blue-600/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Upload className="h-10 w-10 text-blue-400" />
              </div>
              <div className="bg-blue-600 text-white text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4">
                1
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                Scan Your Notes
              </h3>
              <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 group-hover:border-green-500/30 group-hover:bg-gray-800/50 transition-all duration-300">
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 leading-relaxed">
                  Take a photo of your handwritten or printed notes using your
                  camera or upload an existing image. Our advanced OCR
                  technology works with various handwriting styles and document
                  types.
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="bg-purple-600/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Zap className="h-10 w-10 text-purple-400" />
              </div>
              <div className="bg-purple-600 text-white text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4">
                2
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                AI Processing
              </h3>
              <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 group-hover:border-green-500/30 group-hover:bg-gray-800/50 transition-all duration-300">
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 leading-relaxed">
                  Our AI analyzes your notes, extracts key concepts, creates
                  summaries, and identifies important topics. The system
                  understands context and generates relevant study materials
                  automatically.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="bg-green-600/20 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Download className="h-10 w-10 text-green-400" />
              </div>
              <div className="bg-green-600 text-white text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-4">
                3
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                Study & Learn
              </h3>
              <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 group-hover:border-green-500/30 group-hover:bg-gray-800/50 transition-all duration-300">
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 leading-relaxed">
                  Access your digitized notes, generated flashcards, and
                  interactive quizzes. Review summaries, practice with spaced
                  repetition, and track your learning progress.
                </p>
              </div>
            </div>
          </div>

          {/* Process Flow */}
          <div className="mt-20 bg-gradient-to-r from-gray-800/50 via-gray-800/60 to-gray-800/50 backdrop-blur-lg rounded-2xl p-10 border border-gray-700/50 hover:border-gray-600/60 transition-all duration-500 group shadow-xl">
            <h3 className="text-3xl font-bold mb-10 text-center bg-gradient-to-r from-blue-300 via-purple-300 to-green-300 bg-clip-text text-transparent">
              Complete Learning Workflow
            </h3>
            <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0 relative">
              {/* Animated background gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-green-500/5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="flex items-center space-x-4 bg-blue-500/10 px-6 py-4 rounded-full hover:bg-blue-500/20 transition-all duration-300 group/item relative z-10">
                <div className="bg-blue-500/20 p-3 rounded-full group-hover/item:scale-110 transition-transform duration-300">
                  <Camera className="h-8 w-8 text-blue-400 group-hover/item:text-blue-300 transition-colors duration-300" />
                </div>
                <span className="text-white font-semibold text-lg group-hover/item:text-blue-300 transition-colors duration-300">
                  Capture
                </span>
              </div>

              <div className="flex items-center">
                <ArrowRight className="h-8 w-8 text-gray-400 rotate-90 md:rotate-0 hover:text-blue-400 transition-all duration-300 animate-pulse" />
              </div>

              <div className="flex items-center space-x-4 bg-purple-500/10 px-6 py-4 rounded-full hover:bg-purple-500/20 transition-all duration-300 group/item relative z-10">
                <div className="bg-purple-500/20 p-3 rounded-full group-hover/item:scale-110 transition-transform duration-300">
                  <Brain className="h-8 w-8 text-purple-400 group-hover/item:text-purple-300 transition-colors duration-300" />
                </div>
                <span className="text-white font-semibold text-lg group-hover/item:text-purple-300 transition-colors duration-300">
                  Process
                </span>
              </div>

              <div className="flex items-center">
                <ArrowRight className="h-8 w-8 text-gray-400 rotate-90 md:rotate-0 hover:text-purple-400 transition-all duration-300 animate-pulse" />
              </div>

              <div className="flex items-center space-x-4 bg-green-500/10 px-6 py-4 rounded-full hover:bg-green-500/20 transition-all duration-300 group/item relative z-10">
                <div className="bg-green-500/20 p-3 rounded-full group-hover/item:scale-110 transition-transform duration-300">
                  <BookOpen className="h-8 w-8 text-green-400 group-hover/item:text-green-300 transition-colors duration-300" />
                </div>
                <span className="text-white font-semibold text-lg group-hover/item:text-green-300 transition-colors duration-300">
                  Study
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="mt-32">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Start free and upgrade as your learning needs grow
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Free Plan */}
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700 relative">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">Free</h3>
                <div className="text-4xl font-bold text-white mb-2">
                  $0<span className="text-lg text-gray-400">/month</span>
                </div>
                <p className="text-gray-400">Perfect for getting started</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">5 notes per month</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Basic OCR scanning</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">AI summaries</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Basic flashcards</span>
                </li>
                <li className="flex items-center">
                  <X className="h-5 w-5 text-red-400 mr-3" />
                  <span className="text-gray-500">Advanced AI features</span>
                </li>
                <li className="flex items-center">
                  <X className="h-5 w-5 text-red-400 mr-3" />
                  <span className="text-gray-500">Priority support</span>
                </li>
              </ul>

              <button className="w-full bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                Get Started Free
              </button>
            </div>

            {/* Pro Plan */}
            <div className="bg-gradient-to-b from-blue-600/20 to-purple-600/20 backdrop-blur-sm rounded-xl p-8 border border-blue-500 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                  Most Popular
                </span>
              </div>

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">Pro</h3>
                <div className="text-4xl font-bold text-white mb-2">
                  $9<span className="text-lg text-gray-400">/month</span>
                </div>
                <p className="text-gray-400">For serious students</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Unlimited notes</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">
                    Advanced OCR with handwriting
                  </span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">
                    AI summaries & key points
                  </span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">
                    Smart flashcards & quizzes
                  </span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Study analytics</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Priority support</span>
                </li>
              </ul>

              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                Start Pro Trial
              </button>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700 relative">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">
                  Enterprise
                </h3>
                <div className="text-4xl font-bold text-white mb-2">
                  $29<span className="text-lg text-gray-400">/month</span>
                </div>
                <p className="text-gray-400">For teams and institutions</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Everything in Pro</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Team collaboration</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Advanced analytics</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Custom integrations</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">Dedicated support</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-400 mr-3" />
                  <span className="text-gray-300">SSO & security features</span>
                </li>
              </ul>

              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                Contact Sales
              </button>
            </div>
          </div>

          {/* FAQ */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-semibold text-white mb-8">
              Frequently Asked Questions
            </h3>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="text-left">
                <h4 className="text-lg font-semibold text-white mb-2">
                  Can I cancel anytime?
                </h4>
                <p className="text-gray-400">
                  Yes, you can cancel your subscription at any time. Your access
                  will continue until the end of your billing period.
                </p>
              </div>
              <div className="text-left">
                <h4 className="text-lg font-semibold text-white mb-2">
                  Is my data secure?
                </h4>
                <p className="text-gray-400">
                  Absolutely. We use enterprise-grade encryption and never share
                  your personal study materials with third parties.
                </p>
              </div>
              <div className="text-left">
                <h4 className="text-lg font-semibold text-white mb-2">
                  What file formats are supported?
                </h4>
                <p className="text-gray-400">
                  We support JPG, PNG, PDF, and most common image formats. Our
                  OCR works with both printed and handwritten text.
                </p>
              </div>
              <div className="text-left">
                <h4 className="text-lg font-semibold text-white mb-2">
                  Do you offer student discounts?
                </h4>
                <p className="text-gray-400">
                  Yes! Students with valid .edu email addresses receive 50% off
                  Pro plans. Contact support for verification.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-6 py-8 mt-16 border-t border-gray-800">
        <div className="text-center text-gray-400">
          <p>&copy; 2024 SnapStudy. Transform your learning experience.</p>
        </div>
      </footer>
    </div>
  );
}
