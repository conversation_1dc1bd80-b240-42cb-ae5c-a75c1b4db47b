'use client';

import React, { useState } from 'react';
import { Search, Filter, Plus, BookOpen, Calendar, Tag, Eye, Edit, Trash2 } from 'lucide-react';
import AppLayout from '@/components/layout/AppLayout';
import Button from '@/components/ui/Button';
import Card, { CardContent, CardHeader } from '@/components/ui/Card';
import Link from 'next/link';

interface Note {
  id: string;
  title: string;
  subject: string;
  content: string;
  summary: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  flashcardCount: number;
  confidence: number;
}

const mockNotes: Note[] = [
  {
    id: '1',
    title: 'Calculus Chapter 3: Derivatives',
    subject: 'Mathematics',
    content: 'The derivative of a function represents the rate of change...',
    summary: 'Introduction to derivatives, basic rules, and applications in calculus.',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15',
    tags: ['calculus', 'derivatives', 'mathematics'],
    flashcardCount: 8,
    confidence: 92,
  },
  {
    id: '2',
    title: 'World War II Timeline',
    subject: 'History',
    content: 'World War II began in 1939 when Germany invaded Poland...',
    summary: 'Key events and timeline of World War II from 1939 to 1945.',
    createdAt: '2024-01-14',
    updatedAt: '2024-01-14',
    tags: ['wwii', 'timeline', 'history'],
    flashcardCount: 12,
    confidence: 88,
  },
  {
    id: '3',
    title: 'Organic Chemistry Reactions',
    subject: 'Chemistry',
    content: 'Organic reactions involve the breaking and forming of bonds...',
    summary: 'Overview of common organic chemistry reactions and mechanisms.',
    createdAt: '2024-01-13',
    updatedAt: '2024-01-13',
    tags: ['organic', 'reactions', 'chemistry'],
    flashcardCount: 15,
    confidence: 85,
  },
  {
    id: '4',
    title: 'Photosynthesis Process',
    subject: 'Biology',
    content: 'Photosynthesis is the process by which plants convert light energy...',
    summary: 'Detailed explanation of photosynthesis stages and importance.',
    createdAt: '2024-01-12',
    updatedAt: '2024-01-12',
    tags: ['photosynthesis', 'biology', 'plants'],
    flashcardCount: 10,
    confidence: 90,
  },
  {
    id: '5',
    title: 'Shakespeare\'s Hamlet Analysis',
    subject: 'Literature',
    content: 'Hamlet is a tragedy written by William Shakespeare...',
    summary: 'Character analysis and themes in Shakespeare\'s Hamlet.',
    createdAt: '2024-01-11',
    updatedAt: '2024-01-11',
    tags: ['shakespeare', 'hamlet', 'literature'],
    flashcardCount: 6,
    confidence: 87,
  },
];

export default function NotesPage() {
  const [notes, setNotes] = useState<Note[]>(mockNotes);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'subject'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const subjects = Array.from(new Set(notes.map(note => note.subject)));

  const filteredNotes = notes
    .filter(note => {
      const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           note.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesSubject = !selectedSubject || note.subject === selectedSubject;
      return matchesSearch && matchesSubject;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'subject':
          comparison = a.subject.localeCompare(b.subject);
          break;
        case 'date':
        default:
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-400';
    if (confidence >= 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <AppLayout>
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">My Notes</h1>
            <p className="text-gray-400">Manage and review your digitized notes</p>
          </div>
          <Link href="/scan">
            <Button className="mt-4 sm:mt-0">
              <Plus className="h-4 w-4 mr-2" />
              Add New Note
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search notes, content, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Subject Filter */}
              <div className="flex gap-4">
                <select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Subjects</option>
                  {subjects.map(subject => (
                    <option key={subject} value={subject}>{subject}</option>
                  ))}
                </select>

                {/* Sort Options */}
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field as 'date' | 'title' | 'subject');
                    setSortOrder(order as 'asc' | 'desc');
                  }}
                  className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="date-desc">Newest First</option>
                  <option value="date-asc">Oldest First</option>
                  <option value="title-asc">Title A-Z</option>
                  <option value="title-desc">Title Z-A</option>
                  <option value="subject-asc">Subject A-Z</option>
                  <option value="subject-desc">Subject Z-A</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredNotes.map((note) => (
            <Card key={note.id} className="hover:border-blue-500 transition-colors">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-1 line-clamp-2">
                      {note.title}
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-gray-400 mb-2">
                      <Tag className="h-3 w-3" />
                      <span>{note.subject}</span>
                    </div>
                  </div>
                  <div className={`text-sm font-medium ${getConfidenceColor(note.confidence)}`}>
                    {note.confidence}%
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                  {note.summary}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {note.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-blue-600/20 text-blue-400 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                  {note.tags.length > 3 && (
                    <span className="px-2 py-1 bg-gray-600/20 text-gray-400 text-xs rounded-full">
                      +{note.tags.length - 3}
                    </span>
                  )}
                </div>

                {/* Meta Info */}
                <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(note.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-3 w-3" />
                    <span>{note.flashcardCount} cards</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" className="flex-1">
                    <Eye className="h-3 w-3 mr-1" />
                    View
                  </Button>
                  <Button variant="ghost" size="sm" className="flex-1">
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredNotes.length === 0 && (
          <div className="text-center py-16">
            <BookOpen className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No notes found</h3>
            <p className="text-gray-400 mb-6">
              {searchTerm || selectedSubject 
                ? 'Try adjusting your search or filters' 
                : 'Start by scanning your first note'}
            </p>
            <Link href="/scan">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Note
              </Button>
            </Link>
          </div>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-blue-400 mb-1">
                {notes.length}
              </div>
              <div className="text-sm text-gray-400">Total Notes</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-green-400 mb-1">
                {notes.reduce((sum, note) => sum + note.flashcardCount, 0)}
              </div>
              <div className="text-sm text-gray-400">Flashcards</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-purple-400 mb-1">
                {subjects.length}
              </div>
              <div className="text-sm text-gray-400">Subjects</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-yellow-400 mb-1">
                {Math.round(notes.reduce((sum, note) => sum + note.confidence, 0) / notes.length)}%
              </div>
              <div className="text-sm text-gray-400">Avg. Confidence</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
