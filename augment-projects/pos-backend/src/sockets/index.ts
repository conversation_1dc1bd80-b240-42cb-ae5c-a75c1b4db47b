import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { JWT_SECRET } from '../config/env';

// Socket.IO event handlers
export const setupSocketHandlers = (io: Server) => {
  // Middleware for authentication
  io.use((socket, next) => {
    const token = socket.handshake.auth.token;
    
    if (!token) {
      return next(new Error('Authentication required'));
    }

    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (socket as any).user = decoded;
      next();
    } catch (error) {
      next(new Error('Invalid token'));
    }
  });

  // Connection handler
  io.on('connection', (socket: Socket) => {
    console.log(`Socket connected: ${socket.id}`);

    // Join rooms based on user role
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const user = (socket as any).user;
    if (user) {
      socket.join(`user:${user.id}`);
      socket.join(`role:${user.role}`);
    }

    // Handle order updates
    socket.on('order:update', (data) => {
      // Broadcast to all staff
      io.to('role:ADMIN').to('role:MANAGER').to('role:CASHIER').emit('order:updated', data);
    });

    // Handle inventory updates
    socket.on('inventory:update', (data) => {
      // Broadcast to all staff
      io.emit('inventory:updated', data);
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`Socket disconnected: ${socket.id}`);
    });
  });
};
