import { Request, Response, NextFunction } from 'express';
import { NODE_ENV } from '../config/env';

// Custom error class
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error handler middleware
export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) => {
  // Default error status and message
  let statusCode = 500;
  let message = 'Internal Server Error';
  let stack: string | undefined;

  // If it's our custom AppError, use its status code and message
  if ('statusCode' in err) {
    statusCode = err.statusCode;
    message = err.message;
  } else if (err.name === 'ValidationError') {
    // Handle validation errors
    statusCode = 400;
    message = err.message;
  } else if (err.name === 'UnauthorizedError') {
    // Handle JWT errors
    statusCode = 401;
    message = 'Unauthorized';
  }

  // Include stack trace in development mode
  if (NODE_ENV === 'development') {
    stack = err.stack;
  }

  // Log error
  console.error(`[ERROR] ${statusCode} - ${message}`, stack || '');

  // Send response
  res.status(statusCode).json({
    status: 'error',
    message,
    ...(NODE_ENV === 'development' && { stack }),
  });
};
