import { Request, Response } from 'express';
import { Prisma } from '../generated/prisma';

/**
 * Handle errors in controllers
 * @param error Error object
 * @param req Express request
 * @param res Express response
 */
export const handleError = (error: unknown, req: Request, res: Response): void => {
  console.error('Error:', error);

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002': // Unique constraint violation
        res.status(409).json({
          success: false,
          message: 'A record with this information already exists',
          error: error.message,
        });
        break;
      case 'P2025': // Record not found
        res.status(404).json({
          success: false,
          message: 'Record not found',
          error: error.message,
        });
        break;
      case 'P2003': // Foreign key constraint failed
        res.status(400).json({
          success: false,
          message: 'Related record not found',
          error: error.message,
        });
        break;
      default:
        res.status(500).json({
          success: false,
          message: 'Database error',
          error: error.message,
        });
    }
    return;
  }

  // Handle validation errors
  if (error instanceof Prisma.PrismaClientValidationError) {
    res.status(400).json({
      success: false,
      message: 'Validation error',
      error: error.message,
    });
    return;
  }

  // Handle other errors
  if (error instanceof Error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message,
    });
    return;
  }

  // Handle unknown errors
  res.status(500).json({
    success: false,
    message: 'An unexpected error occurred',
    error: 'Unknown error',
  });
};
