// import { PrismaClient } from '@prisma/client';
// import { DeepMockProxy, mockDeep, mockReset } from 'jest-mock-extended';

// // Mock PrismaClient
// jest.mock('../database/client', () => ({
//   __esModule: true,
//   default: mockDeep<PrismaClient>(),
// }));

// // Mock Redis client
// jest.mock('../config/redis', () => ({
//   redisClient: {
//     get: jest.fn(),
//     set: jest.fn(),
//     del: jest.fn(),
//     connect: jest.fn(),
//     on: jest.fn(),
//   },
//   connectRedis: jest.fn(),
// }));

// // Import prisma client after mocking
// import prisma from '../database/client';

// beforeEach(() => {
//   mockReset(prisma as unknown as DeepMockProxy<PrismaClient>);
// });
