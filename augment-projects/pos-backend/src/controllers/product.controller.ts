import { NextFunction, Request, Response } from 'express';
import prisma from '../database/client';
import { AdjustmentType } from '../generated/prisma';
import { AppError } from '../middlewares/errorHandler';

// Get all products
export const getAllProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { page = '1', limit = '10', category, active } = req.query;
    
    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;
    
    // Build filter
    const filter: any = {};
    
    if (category) {
      filter.categoryId = category as string;
    }
    
    if (active !== undefined) {
      filter.isActive = active === 'true';
    }
    
    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where: filter,
        select: {
          id: true,
          sku: true,
          name: true,
          description: true,
          price: true,
          category: {
            select:{
              name: true,
            }
          },
          stockQuantity: true,
          isActive: true,
     
        },
        skip,
        take: limitNumber,
        orderBy: {
          name: 'asc',
        },
      }),
      prisma.product.count({
        where: filter,
      }),
    ]);
    
    res.status(200).json({
      status: 'success',
      data: {
        products,
        pagination: {
          total,
          page: pageNumber,
          limit: limitNumber,
          pages: Math.ceil(total / limitNumber),
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// Get product by ID
export const getProductById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
      },
    });
    
    if (!product) {
      throw new AppError('Product not found', 404);
    }
    
    res.status(200).json({
      status: 'success',
      data: product,
    });
  } catch (error) {
    next(error);
  }
};

// Create product
export const createProduct = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { 
      name, 
      description, 
      barcode, 
      sku, 
      price, 
      costPrice, 
      categoryId, 
      stockQuantity = 0, 
      isActive = true 
    } = req.body;
    
    // Validate required fields
    if (!name || !price) {
      throw new AppError('Name and price are required', 400);
    }
    
    // Check if barcode or SKU already exists
    if (barcode || sku) {
      const existingProduct = await prisma.product.findFirst({
        where: {
          OR: [
            { barcode: barcode || undefined },
            { sku: sku || undefined },
          ],
        },
      });
      
      if (existingProduct) {
        throw new AppError('A product with this barcode or SKU already exists', 400);
      }
    }

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: { id: categoryId },
    });

    if (!existingCategory) {
      throw new AppError('Category not found', 404);
    }
    
    // Create product
    const product = await prisma.product.create({
      data: {
        name,
        description,
        barcode,
        sku,
        price: typeof price === 'string' ? parseFloat(price) : price,
        costPrice: costPrice ? (typeof costPrice === 'string' ? parseFloat(costPrice) : costPrice) : undefined,
        categoryId,
        stockQuantity: parseInt(stockQuantity.toString(), 10),
        isActive,
      },
      include: {
        category: true,
      },
    });
    
    res.status(201).json({
      status: 'success',
      data: product,
    });
  } catch (error) {
    next(error);
  }
};

// Update product
export const updateProduct = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      description, 
      barcode, 
      sku, 
      price, 
      costPrice, 
      categoryId, 
      isActive 
    } = req.body;
    
    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    });
    
    if (!existingProduct) {
      throw new AppError('Product not found', 404);
    }
    
    // Check if barcode or SKU already exists on another product
    if (barcode || sku) {
      const duplicateProduct = await prisma.product.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                { barcode: barcode || undefined },
                { sku: sku || undefined },
              ],
            },
          ],
        },
      });
      
      if (duplicateProduct) {
        throw new AppError('Another product with this barcode or SKU already exists', 400);
      }
    }
    
    // Update product
    const product = await prisma.product.update({
      where: { id },
      data: {
        name,
        description,
        barcode,
        sku,
        price: price !== undefined ? (typeof price === 'string' ? parseFloat(price) : price) : undefined,
        costPrice: costPrice !== undefined ? (typeof costPrice === 'string' ? parseFloat(costPrice) : costPrice) : undefined,
        categoryId,
        isActive,
      },
      include: {
        category: true,
      },
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        product,
      },
    });
  } catch (error) {
    next(error);
  }
};

// Delete product
export const deleteProduct = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    
    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    });
    
    if (!existingProduct) {
      throw new AppError('Product not found', 404);
    }
    
    // Check if product is used in any transaction
    const transactionItems = await prisma.transactionItem.findFirst({
      where: { productId: id },
    });
    
    if (transactionItems) {
      throw new AppError('Cannot delete product that is used in transactions', 400);
    }
    
    // Delete product
    await prisma.product.delete({
      where: { id },
    });
    
    res.status(200).json({
      status: 'success',
      message: 'Product deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// Search products
export const searchProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { query, page = '1', limit = '10' } = req.query;
    
    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;
    
    if (!query) {
      throw new AppError('Search query is required', 400);
    }
    
    const searchQuery = query as string;
    
    // Search products
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where: {
          OR: [
            { name: { contains: searchQuery, mode: 'insensitive' } },
            { description: { contains: searchQuery, mode: 'insensitive' } },
            { barcode: { contains: searchQuery, mode: 'insensitive' } },
            { sku: { contains: searchQuery, mode: 'insensitive' } },
          ],
        },
        include: {
          category: true,
        },
        skip,
        take: limitNumber,
        orderBy: {
          name: 'asc',
        },
      }),
      prisma.product.count({
        where: {
          OR: [
            { name: { contains: searchQuery, mode: 'insensitive' } },
            { description: { contains: searchQuery, mode: 'insensitive' } },
            { barcode: { contains: searchQuery, mode: 'insensitive' } },
            { sku: { contains: searchQuery, mode: 'insensitive' } },
          ],
        },
      }),
    ]);
    
    res.status(200).json({
      status: 'success',
      data: {
        products,
        pagination: {
          total,
          page: pageNumber,
          limit: limitNumber,
          pages: Math.ceil(total / limitNumber),
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// Adjust stock
export const adjustStock = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { quantity, type, reason } = req.body;
    const userId = req.user?.id;
    
    // Validate input
    if (quantity === undefined || !type) {
      throw new AppError('Quantity and adjustment type are required', 400);
    }
    
    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id },
    });
    
    if (!product) {
      throw new AppError('Product not found', 404);
    }
    
    // Calculate new stock quantity
    let newStockQuantity = product.stockQuantity;
    
    switch (type) {
      case AdjustmentType.ADDITION:
        newStockQuantity += parseInt(quantity.toString(), 10);
        break;
      case AdjustmentType.SUBTRACTION:
        newStockQuantity -= parseInt(quantity.toString(), 10);
        if (newStockQuantity < 0) {
          throw new AppError('Stock quantity cannot be negative', 400);
        }
        break;
      case AdjustmentType.INVENTORY_COUNT:
        newStockQuantity = parseInt(quantity.toString(), 10);
        if (newStockQuantity < 0) {
          throw new AppError('Stock quantity cannot be negative', 400);
        }
        break;
      case AdjustmentType.DAMAGED:
        newStockQuantity -= parseInt(quantity.toString(), 10);
        if (newStockQuantity < 0) {
          throw new AppError('Stock quantity cannot be negative', 400);
        }
        break;
      case AdjustmentType.RETURNED:
        newStockQuantity += parseInt(quantity.toString(), 10);
        break;
      default:
        throw new AppError('Invalid adjustment type', 400);
    }
    
    // Update product stock
    const updatedProduct = await prisma.$transaction(async (tx) => {
      // Create stock adjustment record
      await tx.stockAdjustment.create({
        data: {
          productId: id,
          quantity: parseInt(quantity.toString(), 10),
          type,
          reason,
          userId: userId as string,
        },
      });
      
      // Update product stock
      return tx.product.update({
        where: { id },
        data: {
          stockQuantity: newStockQuantity,
        },
        include: {
          category: true,
        },
      });
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        product: updatedProduct,
      },
    });
  } catch (error) {
    next(error);
  }
};
