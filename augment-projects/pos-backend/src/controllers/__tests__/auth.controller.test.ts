import { Request, Response } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { login, logout, refreshToken, changePassword } from '../auth.controller';
import prisma from '../../database/client';
import { UserRole } from '@prisma/client';
import { JWT_SECRET } from '../../config/env';

// Mock bcrypt
jest.mock('bcrypt');

// Mock jwt
jest.mock('jsonwebtoken');

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-refresh-token'),
}));

describe('Auth Controller', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockNext = jest.fn();
  });

  describe('login', () => {
    it('should return 400 if email or password is missing', async () => {
      mockRequest.body = { email: '<EMAIL>' };

      await login(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0].statusCode).toBe(400);
    });

    it('should return 401 if user is not found', async () => {
      mockRequest.body = { email: '<EMAIL>', password: 'password' };
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      await login(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0].statusCode).toBe(401);
    });

    it('should return 401 if password is invalid', async () => {
      mockRequest.body = { email: '<EMAIL>', password: 'password' };
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: '1',
        email: '<EMAIL>',
        password: 'hashedPassword',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.ADMIN,
        isActive: true,
      });
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      await login(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0].statusCode).toBe(401);
    });

    it('should return 200 and tokens if login is successful', async () => {
      mockRequest.body = { email: '<EMAIL>', password: 'password' };
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: '1',
        email: '<EMAIL>',
        password: 'hashedPassword',
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.ADMIN,
        isActive: true,
      });
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      (jwt.sign as jest.Mock).mockReturnValue('mock-jwt-token');
      (prisma.refreshToken.create as jest.Mock).mockResolvedValue({
        id: '1',
        token: 'mock-refresh-token',
        userId: '1',
        expiresAt: new Date(),
        createdAt: new Date(),
      });

      await login(
        mockRequest as Request,
        mockResponse as Response,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'success',
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            role: UserRole.ADMIN,
          },
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token',
        },
      });
    });
  });

  // Add more tests for logout, refreshToken, and changePassword
});
