import { handleError } from '@/utils/errorHandler';
import { NextFunction, Request, Response } from 'express';
import prisma from '../database/client';
import { AppError } from '../middlewares/errorHandler';

/**
 * @desc    Get all configurations
 * @route   GET /api/v1/configurations
 * @access  Private
 */
export const getAllConfigurations = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const configurations = await prisma.configuration.findMany({
      orderBy: {
        key: 'asc',
      },
    });

    res.status(200).json({
      success: true,
      count: configurations.length,
      data: configurations,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get configuration by key
 * @route   GET /api/v1/configurations/:key
 * @access  Private
 */
export const getConfigurationByKey = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { key } = req.params;

    const configuration = await prisma.configuration.findUnique({
      where: { key },
    });

    if (!configuration) {
      throw new AppError('Configuration not found', 404);
    }

    res.status(200).json({
      success: true,
      data: configuration,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Create new configuration
 * @route   POST /api/v1/configurations
 * @access  Private (Admin only)
 */
export const createConfiguration = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { key, value, description } = req.body;

    if (!key || !value) {
      throw new AppError('Key and value are required', 400);
    }

    // Check if configuration with key already exists
    const existingConfiguration = await prisma.configuration.findUnique({
      where: { key },
    });

    if (existingConfiguration) {
      throw new AppError('Configuration with this key already exists', 400);
    }

    const configuration = await prisma.configuration.create({
      data: {
        key,
        value,
        description,
      },
    });

    res.status(201).json({
      success: true,
      data: configuration,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update configuration
 * @route   PUT /api/v1/configurations/:key
 * @access  Private (Admin only)
 */
export const updateConfiguration = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { key } = req.params;
    const { value, description } = req.body;

    if (!value) {
      throw new AppError('Value is required', 400);
    }

    // Check if configuration exists
    const existingConfiguration = await prisma.configuration.findUnique({
      where: { key },
    });

    if (!existingConfiguration) {
      throw new AppError('Configuration not found', 404);
    }

    const configuration = await prisma.configuration.update({
      where: { key },
      data: {
        value,
        description: description !== undefined ? description : existingConfiguration.description,
      },
    });

    res.status(200).json({
      success: true,
      data: configuration,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Delete configuration
 * @route   DELETE /api/v1/configurations/:key
 * @access  Private (Admin only)
 */
export const deleteConfiguration = async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    // Check if configuration exists
    const configuration = await prisma.configuration.findUnique({
      where: { key },
    });

    if (!configuration) {
      return res.status(404).json({
        success: false,
        message: 'Configuration not found',
      });
    }

    // Delete configuration
    await prisma.configuration.delete({
      where: { key },
    });

    res.status(200).json({
      success: true,
      message: 'Configuration deleted successfully',
    });
  } catch (error) {
    handleError(error, req, res);
  }
};
