import { NextFunction, Request, Response } from 'express';
import { PrismaClient } from '../generated/prisma';

const prisma = new PrismaClient();

/**
 * @desc    Get daily sales report
 * @route   GET /api/v1/reports/daily-sales
 * @access  Private (Manager, Admin)
 */
export const getDailySalesReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required',
      });
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    // Set end date to end of day
    end.setHours(23, 59, 59, 999);

    // Get transactions within date range
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end,
        },
        orderStatus: {
          not: 'CANCELLED',
        },
      },
      include: {
        items: true,
      },
    });

    // Group by date
    const dailySales: Record<string, any> = {};

    transactions.forEach((transaction) => {
      const date = transaction.createdAt.toISOString().split('T')[0];

      if (!dailySales[date]) {
        dailySales[date] = {
          date,
          totalSales: 0,
          totalTransactions: 0,
          totalItems: 0,
          totalTax: 0,
          totalDiscount: 0,
          paymentMethods: {},
        };
      }

      dailySales[date].totalSales += transaction.total.toNumber();
      dailySales[date].totalTransactions += 1;
      dailySales[date].totalItems += transaction.items.length;
      dailySales[date].totalTax += transaction.taxAmount.toNumber();
      dailySales[date].totalDiscount += transaction.discountAmount.toNumber();

      // Count payment methods
      const paymentMethod = transaction.paymentMethod;
      if (!dailySales[date].paymentMethods[paymentMethod]) {
        dailySales[date].paymentMethods[paymentMethod] = 0;
      }
      dailySales[date].paymentMethods[paymentMethod] += 1;
    });

    // Convert to array and sort by date
    const result = Object.values(dailySales).sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Calculate summary
    const summary = {
      totalSales: result.reduce((sum, day) => sum + day.totalSales, 0),
      totalTransactions: result.reduce((sum, day) => sum + day.totalTransactions, 0),
      totalItems: result.reduce((sum, day) => sum + day.totalItems, 0),
      totalTax: result.reduce((sum, day) => sum + day.totalTax, 0),
      totalDiscount: result.reduce((sum, day) => sum + day.totalDiscount, 0),
      averageSalePerDay: result.length > 0
        ? result.reduce((sum, day) => sum + day.totalSales, 0) / result.length
        : 0,
      averageTransactionValue: result.reduce((sum, day) => sum + day.totalTransactions, 0) > 0
        ? result.reduce((sum, day) => sum + day.totalSales, 0) / result.reduce((sum, day) => sum + day.totalTransactions, 0)
        : 0,
    };

    res.status(200).json({
      success: true,
      data: result,
      summary,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get product sales report
 * @route   GET /api/v1/reports/product-sales
 * @access  Private (Manager, Admin)
 */
export const getProductSalesReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { startDate, endDate, categoryId } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required',
      });
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    // Set end date to end of day
    end.setHours(23, 59, 59, 999);

    // Build where clause
    const whereClause: any = {
      transaction: {
        createdAt: {
          gte: start,
          lte: end,
        },
        orderStatus: {
          not: 'CANCELLED',
        },
      },
    };

    if (categoryId) {
      whereClause.product = {
        categoryId: categoryId as string,
      };
    }

    // Get transaction items within date range
    const transactionItems = await prisma.transactionItem.findMany({
      where: whereClause,
      include: {
        product: {
          include: {
            category: true,
          },
        },
      },
    });

    // Group by product
    const productSales: Record<string, any> = {};

    transactionItems.forEach((item) => {
      const productId = item.productId;

      if (!productSales[productId]) {
        productSales[productId] = {
          productId,
          productName: item.product.name,
          categoryName: item.product.category?.name || 'Uncategorized',
          totalQuantity: 0,
          totalSales: 0,
          totalDiscount: 0,
          totalTax: 0,
          averagePrice: 0,
        };
      }

      productSales[productId].totalQuantity += item.quantity;
      productSales[productId].totalSales += item.total.toNumber();
      productSales[productId].totalDiscount += item.discount.toNumber();
      productSales[productId].totalTax += item.tax.toNumber();
    });

    // Calculate average price
    Object.values(productSales).forEach((product: any) => {
      product.averagePrice = product.totalQuantity > 0
        ? product.totalSales / product.totalQuantity
        : 0;
    });

    // Convert to array and sort by total sales
    const result = Object.values(productSales).sort((a: any, b: any) =>
      b.totalSales - a.totalSales
    );

    // Calculate summary
    const summary = {
      totalProducts: result.length,
      totalQuantity: result.reduce((sum, product: any) => sum + product.totalQuantity, 0),
      totalSales: result.reduce((sum, product: any) => sum + product.totalSales, 0),
      totalDiscount: result.reduce((sum, product: any) => sum + product.totalDiscount, 0),
      totalTax: result.reduce((sum, product: any) => sum + product.totalTax, 0),
    };

    res.status(200).json({
      success: true,
      data: result,
      summary,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get staff performance report
 * @route   GET /api/v1/reports/staff-performance
 * @access  Private (Manager, Admin)
 */
export const getStaffPerformanceReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required',
      });
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    // Set end date to end of day
    end.setHours(23, 59, 59, 999);

    // Get transactions within date range
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end,
        },
        orderStatus: {
          not: 'CANCELLED',
        },
      },
      include: {
        user: true,
        items: true,
      },
    });

    // Group by staff
    const staffPerformance: Record<string, any> = {};

    transactions.forEach((transaction) => {
      const userId = transaction.userId;
      const userName = `${transaction.user.firstName} ${transaction.user.lastName}`;

      if (!staffPerformance[userId]) {
        staffPerformance[userId] = {
          userId,
          userName,
          totalSales: 0,
          totalTransactions: 0,
          totalItems: 0,
          averageTransactionValue: 0,
        };
      }

      staffPerformance[userId].totalSales += transaction.total.toNumber();
      staffPerformance[userId].totalTransactions += 1;
      staffPerformance[userId].totalItems += transaction.items.length;
    });

    // Calculate average transaction value
    Object.values(staffPerformance).forEach((staff: any) => {
      staff.averageTransactionValue = staff.totalTransactions > 0
        ? staff.totalSales / staff.totalTransactions
        : 0;
    });

    // Convert to array and sort by total sales
    const result = Object.values(staffPerformance).sort((a: any, b: any) =>
      b.totalSales - a.totalSales
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get inventory report
 * @route   GET /api/v1/reports/inventory
 * @access  Private (Manager, Admin)
 */
export const getInventoryReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { categoryId, lowStock } = req.query;

    // Build where clause
    const whereClause: any = {};

    if (categoryId) {
      whereClause.categoryId = categoryId as string;
    }

    if (lowStock === 'true') {
      whereClause.stockQuantity = {
        lte: 10, // Example threshold for low stock
      };
    }

    // Get products
    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        category: true,
        stockAdjustments: {
          take: 5,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        stockQuantity: 'asc',
      },
    });

    // Calculate inventory value
    const inventoryValue = products.reduce((sum, product) => {
      const costPrice = product.costPrice?.toNumber() || 0;
      return sum + (costPrice * product.stockQuantity);
    }, 0);

    // Group by category
    const categorySummary: Record<string, any> = {};

    products.forEach((product) => {
      const categoryName = product.category?.name || 'Uncategorized';

      if (!categorySummary[categoryName]) {
        categorySummary[categoryName] = {
          categoryName,
          productCount: 0,
          totalStock: 0,
          inventoryValue: 0,
        };
      }

      categorySummary[categoryName].productCount += 1;
      categorySummary[categoryName].totalStock += product.stockQuantity;
      categorySummary[categoryName].inventoryValue += (product.costPrice?.toNumber() || 0) * product.stockQuantity;
    });

    // Convert to array
    const categorySummaryArray = Object.values(categorySummary);

    // Summary statistics
    const summary = {
      totalProducts: products.length,
      totalStock: products.reduce((sum, product) => sum + product.stockQuantity, 0),
      inventoryValue,
      lowStockCount: products.filter(product => product.stockQuantity <= 10).length,
      outOfStockCount: products.filter(product => product.stockQuantity === 0).length,
    };

    res.status(200).json({
      success: true,
      data: products,
      categorySummary: categorySummaryArray,
      summary,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Export sales report
 * @route   GET /api/v1/reports/export/sales
 * @access  Private (Manager, Admin)
 */
export const exportSalesReport = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { startDate, endDate, format = 'json' } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required',
      });
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    // Set end date to end of day
    end.setHours(23, 59, 59, 999);

    // Get transactions within date range
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end,
        },
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        customer: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Format for export
    const formattedData = transactions.map(transaction => ({
      transactionId: transaction.id,
      transactionNumber: transaction.transactionNumber,
      date: transaction.createdAt.toISOString(),
      customer: transaction.customer ? transaction.customer.name : 'Walk-in Customer',
      cashier: `${transaction.user.firstName} ${transaction.user.lastName}`,
      subtotal: transaction.subtotal.toNumber(),
      tax: transaction.taxAmount.toNumber(),
      discount: transaction.discountAmount.toNumber(),
      total: transaction.total.toNumber(),
      paymentMethod: transaction.paymentMethod,
      paymentStatus: transaction.paymentStatus,
      orderStatus: transaction.orderStatus,
      items: transaction.items.map(item => ({
        product: item.product.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice.toNumber(),
        subtotal: item.subtotal.toNumber(),
        discount: item.discount.toNumber(),
        tax: item.tax.toNumber(),
        total: item.total.toNumber(),
      })),
    }));

    // Return data in requested format
    if (format === 'csv') {
      // In a real implementation, we would convert to CSV here
      // For simplicity, we're just returning JSON
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename=sales-report-${start.toISOString().split('T')[0]}-to-${end.toISOString().split('T')[0]}.json`);
    } else {
      res.setHeader('Content-Type', 'application/json');
    }

    res.status(200).json({
      success: true,
      data: formattedData,
      meta: {
        startDate: start.toISOString(),
        endDate: end.toISOString(),
        totalTransactions: formattedData.length,
        totalSales: formattedData.reduce((sum, transaction) => sum + transaction.total, 0),
      },
    });
  } catch (error) {
    next(error);
  }
};
