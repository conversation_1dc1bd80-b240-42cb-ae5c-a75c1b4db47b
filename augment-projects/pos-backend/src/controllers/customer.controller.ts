import { AppError } from '@/middlewares/errorHandler';
import { NextFunction, Request, Response } from 'express';
import prisma from '../database/client';

/**
 * @desc    Get all customers
 * @route   GET /api/v1/customers
 * @access  Private
 */
export const getAllCustomers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const customers = await prisma.customer.findMany({
      skip,
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });

    const total = await prisma.customer.count();

    res.status(200).json({
      success: true,
      count: customers.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: customers,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get customer by ID
 * @route   GET /api/v1/customers/:id
 * @access  Private
 */
export const getCustomerById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const customer = await prisma.customer.findUnique({
      where: { id },
    });

    if (!customer) {
      throw new AppError('Customer not found', 404);
    }

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Search customers
 * @route   GET /api/v1/customers/search
 * @access  Private
 */
export const searchCustomers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { query } = req.query;

    if (!query) {
      throw new AppError('Search query is required', 400);
    }

    const customers = await prisma.customer.findMany({
      where: {
        OR: [
          { name: { contains: query as string, mode: 'insensitive' } },
          { email: { contains: query as string, mode: 'insensitive' } },
          { phone: { contains: query as string, mode: 'insensitive' } },
        ],
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    res.status(200).json({
      success: true,
      count: customers.length,
      data: customers,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Create new customer
 * @route   POST /api/v1/customers
 * @access  Private (Cashier, Manager, Admin)
 */
export const createCustomer = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, email, phone, address, notes } = req.body;

    if (!name) {
      throw new AppError('Name is required', 400);
    }

    // Check if customer with email already exists
    if (email) {
      const existingCustomer = await prisma.customer.findUnique({
        where: { email },
      });

      if (existingCustomer) {
        throw new AppError('Customer with this email already exists', 400);
      }
    }

    const customer = await prisma.customer.create({
      data: {
        name,
        email,
        phone,
        address,
        notes,
      },
    });

    res.status(201).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update customer
 * @route   PUT /api/v1/customers/:id
 * @access  Private (Cashier, Manager, Admin)
 */
export const updateCustomer = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { name, email, phone, address, notes } = req.body;

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id },
    });

    if (!existingCustomer) {
      throw new AppError('Customer not found', 404);
    }

    // Check if email is already taken by another customer
    if (email && email !== existingCustomer.email) {
      const emailExists = await prisma.customer.findUnique({
        where: { email },
      });

      if (emailExists) {
        throw new AppError('Email is already taken by another customer', 400);
      }
    }

    const customer = await prisma.customer.update({
      where: { id },
      data: {
        name: name || existingCustomer.name,
        email: email || existingCustomer.email,
        phone: phone !== undefined ? phone : existingCustomer.phone,
        address: address !== undefined ? address : existingCustomer.address,
        notes: notes !== undefined ? notes : existingCustomer.notes,
      },
    });

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Delete customer
 * @route   DELETE /api/v1/customers/:id
 * @access  Private (Admin, Manager)
 */
export const deleteCustomer = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id },
    });

    if (!customer) {
      throw new AppError('Customer not found', 404);
    }

    // Delete customer
    await prisma.customer.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update customer loyalty points
 * @route   PUT /api/v1/customers/:id/loyalty-points
 * @access  Private (Cashier, Manager, Admin)
 */
export const updateLoyaltyPoints = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { points, operation } = req.body;

    if (!points || !operation) {
      throw new AppError('Points and operation are required', 400);
    }

    if (!['add', 'subtract', 'set'].includes(operation)) {
      throw new AppError('Operation must be add, subtract, or set', 400);
    }

    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id },
    });

    if (!customer) {
      throw new AppError('Customer not found', 404);
    }

    let newPoints = customer.loyaltyPoints;

    switch (operation) {
      case 'add':
        newPoints += points;
        break;
      case 'subtract':
        newPoints = Math.max(0, customer.loyaltyPoints - points);
        break;
      case 'set':
        newPoints = Math.max(0, points);
        break;
    }

    const updatedCustomer = await prisma.customer.update({
      where: { id },
      data: {
        loyaltyPoints: newPoints,
      },
    });

    res.status(200).json({
      success: true,
      data: updatedCustomer,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get customer transactions
 * @route   GET /api/v1/customers/:id/transactions
 * @access  Private
 */
export const getCustomerTransactions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id },
    });

    if (!customer) {
      throw new AppError('Customer not found', 404);
    }

    const transactions = await prisma.transaction.findMany({
      where: {
        customerId: id,
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });

    const total = await prisma.transaction.count({
      where: {
        customerId: id,
      },
    });

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: transactions,
    });
  } catch (error) {
    next(error);
  }
};
