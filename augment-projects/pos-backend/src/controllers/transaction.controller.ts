import { OrderStatus, PrismaClient } from '../generated/prisma';
import { NextFunction, Request, Response } from 'express';
const prisma = new PrismaClient();

/**
 * @desc    Get all transactions
 * @route   GET /api/v1/transactions
 * @access  Private
 */
export const getAllTransactions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const transactions = await prisma.transaction.findMany({
      skip,
      take: limit,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const total = await prisma.transaction.count();

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: transactions,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get transaction by ID
 * @route   GET /api/v1/transactions/:id
 * @access  Private
 */
export const getTransactionById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        customer: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    res.status(200).json({
      success: true,
      data: transaction,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get transaction items
 * @route   GET /api/v1/transactions/:id/items
 * @access  Private
 */
export const getTransactionItems = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const transaction = await prisma.transaction.findUnique({
      where: { id },
      select: {
        id: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    res.status(200).json({
      success: true,
      count: transaction.items.length,
      data: transaction.items,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Search transactions
 * @route   GET /api/v1/transactions/search
 * @access  Private
 */
export const searchTransactions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { query, startDate, endDate, status } = req.query;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const whereClause: any = {};

    if (query) {
      whereClause.OR = [
        { transactionNumber: { contains: query as string, mode: 'insensitive' } },
        { customer: { name: { contains: query as string, mode: 'insensitive' } } },
      ];
    }

    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string),
      };
    } else if (startDate) {
      whereClause.createdAt = {
        gte: new Date(startDate as string),
      };
    } else if (endDate) {
      whereClause.createdAt = {
        lte: new Date(endDate as string),
      };
    }

    if (status) {
      whereClause.orderStatus = status as OrderStatus;
    }

    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        createdAt: 'desc',
      },
    });

    const total = await prisma.transaction.count({
      where: whereClause,
    });

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      data: transactions,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Create new transaction
 * @route   POST /api/v1/transactions
 * @access  Private (Cashier, Manager, Admin)
 */
export const createTransaction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const {
      customerId,
      items,
      paymentMethod,
      paymentStatus,
      orderStatus,
      notes,
    } = req.body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Transaction items are required',
      });
    }

    if (!paymentMethod) {
      return res.status(400).json({
        success: false,
        message: 'Payment method is required',
      });
    }

    // Generate transaction number
    const transactionNumber = `TXN-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Calculate totals
    let subtotal = 0;
    let taxAmount = 0;
    let discountAmount = 0;
    let total = 0;

    // Prepare transaction items
    const transactionItems = [];

    for (const item of items) {
      const { productId, quantity, discount = 0, tax = 0 } = item;

      if (!productId || !quantity || quantity <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Product ID and quantity are required for each item',
        });
      }

      // Get product details
      const product = await prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        return res.status(400).json({
          success: false,
          message: `Product with ID ${productId} not found`,
        });
      }

      if (product.stockQuantity < quantity) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for product ${product.name}`,
        });
      }

      const itemSubtotal = product.price.toNumber() * quantity;
      const itemDiscount = (discount / 100) * itemSubtotal;
      const itemTax = (tax / 100) * (itemSubtotal - itemDiscount);
      const itemTotal = itemSubtotal - itemDiscount + itemTax;

      subtotal += itemSubtotal;
      discountAmount += itemDiscount;
      taxAmount += itemTax;
      total += itemTotal;

      transactionItems.push({
        productId,
        quantity,
        unitPrice: product.price,
        subtotal: itemSubtotal,
        discount: itemDiscount,
        tax: itemTax,
        total: itemTotal,
      });

      // Update product stock
      await prisma.product.update({
        where: { id: productId },
        data: {
          stockQuantity: {
            decrement: quantity,
          },
        },
      });

      // Record stock adjustment
      await prisma.stockAdjustment.create({
        data: {
          productId,
          quantity,
          type: 'SUBTRACTION',
          reason: 'Sale',
          userId: (req as any).user.id,
        },
      });
    }

    // Create transaction
    const transaction = await prisma.transaction.create({
      data: {
        transactionNumber,
        customerId,
        userId: (req as any).user.id,
        subtotal,
        taxAmount,
        discountAmount,
        total,
        paymentMethod,
        paymentStatus: paymentStatus || 'PENDING',
        orderStatus: orderStatus || 'OPEN',
        notes,
        items: {
          create: transactionItems,
        },
      },
      include: {
        items: true,
        customer: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    // Update customer loyalty points if customer exists
    if (customerId) {
      const pointsToAdd = Math.floor(total / 100); // Example: 1 point per $100 spent
      if (pointsToAdd > 0) {
        await prisma.customer.update({
          where: { id: customerId },
          data: {
            loyaltyPoints: {
              increment: pointsToAdd,
            },
          },
        });
      }
    }

    res.status(201).json({
      success: true,
      data: transaction,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update transaction
 * @route   PUT /api/v1/transactions/:id
 * @access  Private (Admin, Manager)
 */
export const updateTransaction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { customerId, paymentMethod, paymentStatus, orderStatus, notes } = req.body;

    // Check if transaction exists
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id },
    });

    if (!existingTransaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // Update transaction
    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        customerId: customerId !== undefined ? customerId : existingTransaction.customerId,
        paymentMethod: paymentMethod || existingTransaction.paymentMethod,
        paymentStatus: paymentStatus || existingTransaction.paymentStatus,
        orderStatus: orderStatus || existingTransaction.orderStatus,
        notes: notes !== undefined ? notes : existingTransaction.notes,
      },
      include: {
        items: true,
        customer: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: transaction,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Update transaction status
 * @route   PUT /api/v1/transactions/:id/status
 * @access  Private (Cashier, Manager, Admin)
 */
export const updateTransactionStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { paymentStatus, orderStatus } = req.body;

    if (!paymentStatus && !orderStatus) {
      return res.status(400).json({
        success: false,
        message: 'Payment status or order status is required',
      });
    }

    // Check if transaction exists
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id },
    });

    if (!existingTransaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // Update transaction status
    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        paymentStatus: paymentStatus || existingTransaction.paymentStatus,
        orderStatus: orderStatus || existingTransaction.orderStatus,
      },
    });

    res.status(200).json({
      success: true,
      data: transaction,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Delete transaction
 * @route   DELETE /api/v1/transactions/:id
 * @access  Private (Admin, Manager)
 */
export const deleteTransaction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Check if transaction exists
    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        items: true,
      },
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found',
      });
    }

    // Restore product stock quantities
    for (const item of transaction.items) {
      await prisma.product.update({
        where: { id: item.productId },
        data: {
          stockQuantity: {
            increment: item.quantity,
          },
        },
      });

      // Record stock adjustment
      await prisma.stockAdjustment.create({
        data: {
          productId: item.productId,
          quantity: item.quantity,
          type: 'ADDITION',
          reason: 'Transaction deleted',
          userId: (req as any).user.id,
        },
      });
    }

    // Delete transaction (cascade will delete items)
    await prisma.transaction.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: 'Transaction deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};
