import { Router } from 'express';
import {
    exportSalesReport,
    getDailySalesReport,
    getInventoryReport,
    getProductSalesReport,
    getStaffPerformanceReport
} from '../controllers/report.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All report routes require authentication
router.use(authenticate);

// Routes requiring manager or admin privileges
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
// router.get('/daily-sales', getDailySalesReport);
// router.get('/product-sales', getProductSalesReport);
// router.get('/staff-performance', getStaffPerformanceReport);
router.get('/inventory', getInventoryReport);
// router.get('/export/sales', exportSalesReport);

export default router;
