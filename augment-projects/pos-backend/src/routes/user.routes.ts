import { Router } from 'express';
import {
    createUser,
    deleteUser,
    getAllUsers,
    getCurrentUser,
    getUserById,
    updateUser
} from '../controllers/user.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All user routes require authentication
router.use(authenticate);

// Get current user
router.get('/me', getCurrentUser);

// Admin-only routes
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
router.get('/', getAllUsers);
router.get('/:id', getUserById);
router.post('/', createUser);
router.put('/:id', updateUser);
router.delete('/:id', deleteUser);

export default router;
