import { Router } from 'express';
import {
    createConfiguration,
    deleteConfiguration,
    getAllConfigurations,
    getConfigurationByKey,
    updateConfiguration
} from '../controllers/configuration.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All configuration routes require authentication
router.use(authenticate);

// Routes accessible to all authenticated users
router.get('/', getAllConfigurations);
router.get('/:key', getConfigurationByKey);

// Routes requiring admin privileges
router.use(authorize([UserRole.ADMIN]));
router.post('/', createConfiguration);
router.put('/:key', updateConfiguration);
// router.delete('/:key', deleteConfiguration);

export default router;
