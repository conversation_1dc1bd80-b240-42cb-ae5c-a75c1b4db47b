import { Router } from 'express';
import {
  createTransaction,
  deleteTransaction,
  getAllTransactions,
  getTransactionById,
  getTransactionItems,
  searchTransactions,
  updateTransaction,
  updateTransactionStatus,
} from '../controllers/transaction.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All transaction routes require authentication
router.use(authenticate);

// Routes accessible to all authenticated users
router.get('/', getAllTransactions);
router.get('/search', searchTransactions);
// Make sure the specific routes come before the parameterized routes
// router.get('/:id/items', getTransactionItems);
// router.get('/:id', getTransactionById);

// Routes requiring cashier or higher privileges
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER]));
// router.post('/', createTransaction);
// router.put('/:id/status', updateTransactionStatus);

// Admin-only routes
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
// router.put('/:id', updateTransaction);
// router.delete('/:id', deleteTransaction);

export default router;
