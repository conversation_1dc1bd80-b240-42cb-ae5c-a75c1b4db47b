import { Router } from 'express';
import {
    createCategory,
    deleteCategory,
    getAllCategories,
    getCategoryById,
    getCategoryProducts,
    updateCategory} from '../controllers/category.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All category routes require authentication
router.use(authenticate);

// Routes accessible to all authenticated users
router.get('/', getAllCategories);
router.get('/:id', getCategoryById);
router.get('/:id/products', getCategoryProducts);

// Routes requiring higher privileges
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
router.post('/', createCategory);
router.put('/:id', updateCategory);
router.delete('/:id', deleteCategory);

export default router;
