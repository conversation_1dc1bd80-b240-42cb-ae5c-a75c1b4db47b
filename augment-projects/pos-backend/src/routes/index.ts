import { Router } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes';
import productRoutes from './product.routes';
import categoryRoutes from './category.routes';
import customerRoutes from './customer.routes';
import transactionRoutes from './transaction.routes';
import reportRoutes from './report.routes';
import configurationRoutes from './configuration.routes';

export const setupRoutes = () => {
  const router = Router();

  // Register all routes
  router.use('/auth', authRoutes);
  router.use('/users', userRoutes);
  router.use('/products', productRoutes);
  router.use('/categories', categoryRoutes);
  router.use('/customers', customerRoutes);
  router.use('/transactions', transactionRoutes);
  router.use('/reports', reportRoutes);
  router.use('/configurations', configurationRoutes);

  return router;
};
