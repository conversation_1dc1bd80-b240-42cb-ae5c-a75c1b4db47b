import { Router } from 'express';
import {
    createCustomer,
    deleteCustomer,
    getAllCustomers,
    getCustomerById,
    getCustomerTransactions,
    searchCustomers,
    updateCustomer,
    updateLoyaltyPoints
} from '../controllers/customer.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All customer routes require authentication
router.use(authenticate);

// Routes accessible to all authenticated users
router.get('/', getAllCustomers);
router.get('/search', searchCustomers);
router.get('/:id', getCustomerById);
router.get('/:id/transactions', getCustomerTransactions);

// Routes requiring cashier or higher privileges
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER, UserRole.CASHIER]));
router.post('/', createCustomer);
router.put('/:id', updateCustomer);
router.put('/:id/loyalty-points', updateLoyaltyPoints);

// Admin-only routes
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
router.delete('/:id', deleteCustomer);

export default router;
