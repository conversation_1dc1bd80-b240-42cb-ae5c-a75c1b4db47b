import { Router } from 'express';
import {
    adjustStock,
    createProduct,
    deleteProduct,
    getAllProducts,
    getProductById,
    searchProducts,
    updateProduct
} from '../controllers/product.controller';
import { UserRole } from '../generated/prisma';
import { authenticate, authorize } from '../middlewares/auth';

const router = Router();

// All product routes require authentication
router.use(authenticate);

// Routes accessible to all authenticated users
router.get('/', getAllProducts);
router.get('/search', searchProducts);
router.get('/:id', getProductById);

// Routes requiring higher privileges
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
router.post('/', createProduct);
router.put('/:id', updateProduct);
router.delete('/:id', deleteProduct);
router.post('/:id/adjust-stock', adjustStock);

export default router;
