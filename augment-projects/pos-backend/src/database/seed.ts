import bcrypt from 'bcrypt';
import { UserRole } from '../generated/prisma';
import prisma from './client';

async function main() {
  console.log('Starting seed...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
    },
  });
  console.log('Admin user created:', admin.email);

  // Create cashier user
  const cashierPassword = await bcrypt.hash('cashier123', 10);
  const cashier = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: cashierPassword,
      firstName: 'Cashier',
      lastName: 'User',
      role: UserRole.CASHIER,
    },
  });
  console.log('Cashier user created:', cashier.email);

  // Create product categories
  const categories = [
    { name: 'Beverages', description: 'Drinks and liquids' },
    { name: 'Bakery', description: 'Bread and baked goods' },
    { name: 'Canned Goods', description: 'Canned and jarred items' },
    { name: 'Dairy', description: 'Milk, cheese, and other dairy products' },
    { name: 'Produce', description: 'Fruits and vegetables' },
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { name: category.name },
      update: {},
      create: category,
    });
  }
  console.log('Categories created');

  // Create sample products
  const products = [
    {
      name: 'Mineral Water',
      description: '500ml bottled water',
      barcode: '1000000001',
      sku: 'BEV-001',
      price: 1.50,
      costPrice: 0.75,
      categoryId: (await prisma.category.findUnique({ where: { name: 'Beverages' } }))?.id,
      stockQuantity: 100,
    },
    {
      name: 'Whole Wheat Bread',
      description: 'Fresh baked whole wheat bread',
      barcode: '1000000002',
      sku: 'BAK-001',
      price: 3.99,
      costPrice: 1.50,
      categoryId: (await prisma.category.findUnique({ where: { name: 'Bakery' } }))?.id,
      stockQuantity: 30,
    },
    {
      name: 'Canned Beans',
      description: '400g canned beans',
      barcode: '1000000003',
      sku: 'CAN-001',
      price: 2.49,
      costPrice: 1.20,
      categoryId: (await prisma.category.findUnique({ where: { name: 'Canned Goods' } }))?.id,
      stockQuantity: 50,
    },
    {
      name: 'Milk',
      description: '1L fresh milk',
      barcode: '1000000004',
      sku: 'DAI-001',
      price: 2.99,
      costPrice: 1.80,
      categoryId: (await prisma.category.findUnique({ where: { name: 'Dairy' } }))?.id,
      stockQuantity: 40,
    },
    {
      name: 'Apples',
      description: 'Fresh red apples (1kg)',
      barcode: '1000000005',
      sku: 'PRO-001',
      price: 4.99,
      costPrice: 2.50,
      categoryId: (await prisma.category.findUnique({ where: { name: 'Produce' } }))?.id,
      stockQuantity: 25,
    },
  ];

  for (const product of products) {
    await prisma.product.upsert({
      where: { barcode: product.barcode },
      update: {},
      create: product,
    });
  }
  console.log('Products created');

  // Create sample customers
  const customers = [
    {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      address: '123 Main St',
      loyaltyPoints: 100,
    },
    {
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '0987654321',
      address: '456 Oak Ave',
      loyaltyPoints: 50,
    },
  ];

  for (const customer of customers) {
    await prisma.customer.upsert({
      where: { email: customer.email },
      update: {},
      create: customer,
    });
  }
  console.log('Customers created');

  // Create default configurations
  const configurations = [
    { key: 'TAX_RATE', value: '10', description: 'Default tax rate percentage' },
    { key: 'STORE_NAME', value: 'My POS Store', description: 'Store name' },
    { key: 'STORE_ADDRESS', value: '123 Business St', description: 'Store address' },
    { key: 'STORE_PHONE', value: '************', description: 'Store phone number' },
    { key: 'RECEIPT_FOOTER', value: 'Thank you for shopping with us!', description: 'Text to display at the bottom of receipts' },
    { key: 'LOYALTY_POINTS_RATE', value: '10', description: 'Points earned per dollar spent' },
  ];

  for (const config of configurations) {
    await prisma.configuration.upsert({
      where: { key: config.key },
      update: {},
      create: config,
    });
  }
  console.log('Configurations created');

  console.log('Seed completed successfully');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
