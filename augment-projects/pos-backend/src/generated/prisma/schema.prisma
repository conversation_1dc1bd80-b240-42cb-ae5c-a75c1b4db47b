// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and Authentication Models
model User {
  id               String            @id @default(uuid())
  email            String            @unique
  password         String
  firstName        String
  lastName         String
  role             UserRole          @default(STAFF)
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  refreshTokens    RefreshToken[]
  transactions     Transaction[]
  stockAdjustments StockAdjustment[]
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())
}

// Product and Inventory Models
model Category {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Product {
  id               String            @id @default(uuid())
  name             String
  description      String?
  barcode          String?           @unique
  sku              String?           @unique
  price            Decimal           @db.Decimal(10, 2)
  costPrice        Decimal?          @db.Decimal(10, 2)
  categoryId       String?
  category         Category?         @relation(fields: [categoryId], references: [id])
  stockQuantity    Int               @default(0)
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  stockAdjustments StockAdjustment[]
  transactionItems TransactionItem[]
}

model StockAdjustment {
  id        String         @id @default(uuid())
  productId String
  product   Product        @relation(fields: [productId], references: [id])
  quantity  Int
  type      AdjustmentType
  reason    String?
  userId    String
  user      User           @relation(fields: [userId], references: [id])
  createdAt DateTime       @default(now())
}

// Sales and Transaction Models
model Transaction {
  id                String            @id @default(uuid())
  transactionNumber String            @unique
  customerId        String?
  customer          Customer?         @relation(fields: [customerId], references: [id])
  userId            String
  user              User              @relation(fields: [userId], references: [id])
  items             TransactionItem[]
  subtotal          Decimal           @db.Decimal(10, 2)
  taxAmount         Decimal           @db.Decimal(10, 2)
  discountAmount    Decimal           @default(0) @db.Decimal(10, 2)
  total             Decimal           @db.Decimal(10, 2)
  paymentMethod     PaymentMethod
  paymentStatus     PaymentStatus     @default(PENDING)
  orderStatus       OrderStatus       @default(OPEN)
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
}

model TransactionItem {
  id            String      @id @default(uuid())
  transactionId String
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  productId     String
  product       Product     @relation(fields: [productId], references: [id])
  quantity      Int
  unitPrice     Decimal     @db.Decimal(10, 2)
  subtotal      Decimal     @db.Decimal(10, 2)
  discount      Decimal     @default(0) @db.Decimal(10, 2)
  tax           Decimal     @default(0) @db.Decimal(10, 2)
  total         Decimal     @db.Decimal(10, 2)
}

// Customer Management
model Customer {
  id            String        @id @default(uuid())
  name          String
  email         String?       @unique
  phone         String?
  address       String?
  loyaltyPoints Int           @default(0)
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  transactions  Transaction[]
}

// Configuration Management
model Configuration {
  id          String   @id @default(uuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Enums
enum UserRole {
  ADMIN
  MANAGER
  CASHIER
  STAFF
}

enum PaymentMethod {
  CASH
  CARD
  UPI
  BANK_TRANSFER
  CREDIT
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  PARTIALLY_PAID
}

enum OrderStatus {
  OPEN
  CLOSED
  CANCELLED
  REFUNDED
}

enum AdjustmentType {
  ADDITION
  SUBTRACTION
  INVENTORY_COUNT
  DAMAGED
  RETURNED
}
