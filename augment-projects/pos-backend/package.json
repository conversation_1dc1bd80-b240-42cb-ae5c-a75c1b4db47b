{"name": "pos-backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "test": "jest --coverage", "test:watch": "jest --watch", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "seed": "ts-node src/database/seed.ts"}, "keywords": ["pos", "point-of-sale", "backend", "api"], "author": "", "license": "ISC", "description": "A scalable Point of Sale (POS) backend API built with Node.js, Express, and PostgreSQL", "dependencies": {"@prisma/client": "^6.7.0", "bcrypt": "^5.1.1", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "prisma": "^6.7.0", "redis": "^5.0.1", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.16", "@types/socket.io": "^3.0.1", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}