# POS Backend Project Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Entry Point and Execution Flow](#entry-point-and-execution-flow)
3. [Code Structure](#code-structure)
4. [Database Architecture](#database-architecture)
5. [Docker Configuration](#docker-configuration)
6. [Prisma ORM](#prisma-orm)
7. [API Documentation with Swagger](#api-documentation-with-swagger)
8. [Future Development Guidelines](#future-development-guidelines)

## Project Overview

This is a Point of Sale (POS) backend system built with Node.js, Express, PostgreSQL, and Redis. The system provides APIs for managing products, customers, transactions, users, and reports in a retail environment.

## Entry Point and Execution Flow

### Entry Point

The main entry point of the application is `src/index.ts`. This file initializes the Express application, sets up middleware, connects to databases, and starts the HTTP server.

### Execution Flow

1. **Application Initialization**:
   - The Express app is created
   - HTTP server is created from the Express app
   - Socket.IO server is initialized for real-time communication

2. **Middleware Setup**:
   - Security middleware (helmet)
   - Compression middleware
   - JSON body parser
   - CORS configuration
   - Rate limiting

3. **Route Registration**:
   - API routes are registered with the `setupRoutes()` function
   - Each route module is imported and attached to the main router

4. **Error Handling**:
   - Not found handler for undefined routes
   - Global error handler for catching exceptions

5. **Socket.IO Setup**:
   - WebSocket handlers are configured for real-time updates

6. **Database Connections**:
   - Redis connection is established
   - PostgreSQL connection is handled by Prisma ORM

7. **Server Start**:
   - HTTP server starts listening on the configured port

## Code Structure

### Directory Structure

- **src/**: Main source code directory
  - **config/**: Configuration files
    - **env.ts**: Environment variables
    - **redis.ts**: Redis client configuration
    - **swagger.ts**: Swagger documentation setup
  - **controllers/**: Request handlers for each route
  - **database/**: Database-related files
    - **client.ts**: Prisma client instance
    - **seed.ts**: Database seeding script
  - **generated/**: Generated Prisma client code
  - **middlewares/**: Express middleware functions
    - **auth.ts**: Authentication and authorization middleware
    - **errorHandler.ts**: Global error handling middleware
    - **notFoundHandler.ts**: 404 handler
  - **routes/**: API route definitions
    - **index.ts**: Main router setup
    - **auth.routes.ts**: Authentication routes
    - **user.routes.ts**: User management routes
    - **product.routes.ts**: Product management routes
    - **category.routes.ts**: Category management routes
    - **customer.routes.ts**: Customer management routes
    - **transaction.routes.ts**: Transaction management routes
    - **report.routes.ts**: Reporting routes
    - **configuration.routes.ts**: System configuration routes
  - **sockets/**: WebSocket event handlers
  - **index.ts**: Application entry point
- **prisma/**: Prisma ORM configuration
  - **schema.prisma**: Database schema definition
  - **migrations/**: Database migration files
- **docker-compose.yml**: Docker services configuration
- **Dockerfile**: Docker image definition

### Key Files and Their Purpose

#### src/index.ts

The main application file that initializes the server, middleware, and connects to databases.

#### src/routes/index.ts

Registers all API routes and exports a function to set up the main router.

#### src/middlewares/auth.ts

Contains authentication middleware that verifies JWT tokens and authorization middleware that checks user roles.

#### src/config/env.ts

Loads and exports environment variables used throughout the application.

#### src/config/redis.ts

Sets up the Redis client connection for caching and session management.

#### src/config/swagger.ts

Configures Swagger documentation for the API endpoints.

#### src/controllers/*.controller.ts

Contain the business logic for handling API requests, interacting with the database, and sending responses.

## Database Architecture

### PostgreSQL

PostgreSQL is the primary database used for persistent data storage. The database schema is defined in `prisma/schema.prisma` and includes the following main models:

- **User**: Store user accounts with roles and authentication details
- **RefreshToken**: Manage JWT refresh tokens for authentication
- **Category**: Product categories
- **Product**: Store product information including price, stock, etc.
- **StockAdjustment**: Track inventory changes
- **Transaction**: Store sales transactions
- **TransactionItem**: Individual items in a transaction
- **Customer**: Customer information and loyalty points
- **Configuration**: System configuration settings

### Redis

Redis is used as a caching layer and for session management. It provides:

1. **Caching**: Frequently accessed data can be cached to reduce database load
2. **Session Storage**: User sessions and temporary data
3. **Real-time Data**: Supporting real-time features via pub/sub
4. **Rate Limiting**: Helps implement API rate limiting

The Redis connection is established in `src/config/redis.ts` and the client is exported for use throughout the application.

## Docker Configuration

The project uses Docker for containerization, making it easy to set up and deploy in different environments.

### docker-compose.yml

The `docker-compose.yml` file defines the following services:

1. **postgres**: PostgreSQL database
   - Uses PostgreSQL 15 Alpine image
   - Exposes port 5432
   - Configures database name, user, and password
   - Persists data using a Docker volume

2. **redis**: Redis cache
   - Uses Redis 7 Alpine image
   - Exposes port 6379
   - Configures persistence with appendonly mode
   - Persists data using a Docker volume

3. **pgadmin** (commented out): Database management UI
   - Can be uncommented to enable a web interface for PostgreSQL management

### Dockerfile

The `Dockerfile` uses a multi-stage build process:

1. **Builder Stage**:
   - Uses Node.js 20 Alpine as the base image
   - Installs dependencies
   - Generates Prisma client
   - Builds the TypeScript application

2. **Production Stage**:
   - Uses a minimal Node.js 20 Alpine image
   - Copies only the necessary files from the builder stage
   - Installs only production dependencies
   - Sets up the application to run in production mode

### Why Docker is Needed

Docker provides several benefits for this project:

1. **Consistency**: Ensures the application runs the same way in all environments
2. **Isolation**: Keeps the application and its dependencies isolated
3. **Scalability**: Makes it easier to scale the application
4. **Dependency Management**: Simplifies managing PostgreSQL and Redis dependencies
5. **Deployment**: Streamlines the deployment process

## Prisma ORM

Prisma is used as the ORM (Object-Relational Mapping) tool to interact with the PostgreSQL database.

### Schema Definition

The database schema is defined in `prisma/schema.prisma`. This file defines:

- Database connection
- Data models (tables)
- Relationships between models
- Field types and constraints

### Prisma Client

The Prisma client is generated based on the schema and provides type-safe database access. The client is used in controllers to perform CRUD operations.

### Migrations

Prisma migrations are used to manage database schema changes. The migrations are stored in `prisma/migrations/` directory.

### How to Add New Migrations

To create a new migration when changing the database schema:

1. Update the `schema.prisma` file with your changes
2. Run `npm run prisma:migrate` (which executes `prisma migrate dev`)
3. Provide a meaningful name for the migration when prompted
4. The migration will be applied to the database and a new migration file will be created

### Other Prisma Commands

- `npm run prisma:generate`: Generates the Prisma client based on the schema
- `npm run prisma:studio`: Opens Prisma Studio, a visual database editor
- `npm run seed`: Runs the database seeding script

## API Documentation with Swagger

The API is documented using Swagger/OpenAPI specification.

### Swagger Configuration

The Swagger configuration is defined in `src/config/swagger.ts`. This file sets up:

- API information (title, version, description)
- Server configuration
- Security schemes (JWT authentication)
- API endpoints to scan for documentation

### How to Add API Documentation

To document a new API endpoint:

1. Add JSDoc comments above your controller functions
2. Use Swagger annotations in the comments to describe the endpoint
3. Include request/response schemas, parameters, and security requirements

Example from `auth.controller.ts`:

```typescript
/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User email
 *         password:
 *           type: string
 *           format: password
 *           description: User password
 */
```

### Accessing Swagger Documentation

The Swagger UI is available at `/api/v1/docs` when the server is running. It provides an interactive interface to explore and test the API endpoints.

## Future Development Guidelines

### Adding New API Endpoints

To add a new API endpoint:

1. Create or update a controller function in the appropriate controller file
2. Add Swagger documentation comments above the controller function
3. Add the route in the corresponding route file
4. If creating a new resource type, create a new route file and register it in `src/routes/index.ts`

Example of adding a new endpoint:

```typescript
// In src/controllers/product.controller.ts
/**
 * @swagger
 * /products/featured:
 *   get:
 *     summary: Get featured products
 *     tags: [Products]
 *     responses:
 *       200:
 *         description: List of featured products
 */
export const getFeaturedProducts = async (req, res, next) => {
  // Implementation
};

// In src/routes/product.routes.ts
router.get('/featured', getFeaturedProducts);
```

### Adding New Database Tables

To add a new database table:

1. Update the `prisma/schema.prisma` file with the new model
2. Create a migration using `npm run prisma:migrate`
3. Create corresponding controller and route files if needed
4. Update existing controllers/routes if the new table relates to existing ones

Example of adding a new model:

```prisma
// In prisma/schema.prisma
model Supplier {
  id          String    @id @default(uuid())
  name        String
  contactName String?
  email       String?
  phone       String?
  address     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

// Update Product model to add the relation
model Product {
  // Existing fields...
  supplierId  String?
  supplier    Supplier? @relation(fields: [supplierId], references: [id])
}
```

### Redis Caching Strategies

When implementing new features that could benefit from caching:

1. Import the Redis client from `src/config/redis.ts`
2. Use Redis commands to store and retrieve cached data
3. Implement cache invalidation when data changes

Example of implementing caching:

```typescript
import { redisClient } from '../config/redis';

// Caching data
await redisClient.set('products:featured', JSON.stringify(featuredProducts), {
  EX: 3600 // Expire after 1 hour
});

// Retrieving cached data
const cachedData = await redisClient.get('products:featured');
if (cachedData) {
  return JSON.parse(cachedData);
}
```

### Authentication and Authorization

When adding new routes that require authentication or specific permissions:

1. Use the `authenticate` middleware to require authentication
2. Use the `authorize` middleware with appropriate roles to restrict access

Example:

```typescript
// In a route file
router.use(authenticate); // All routes below require authentication

// Routes accessible to all authenticated users
router.get('/', getAllItems);

// Routes requiring specific roles
router.use(authorize([UserRole.ADMIN, UserRole.MANAGER]));
router.post('/', createItem);
router.put('/:id', updateItem);
router.delete('/:id', deleteItem);
```

### Error Handling

When adding new functionality, follow the established error handling pattern:

1. Use try/catch blocks in controllers
2. Use the `AppError` class for custom errors
3. Let the global error handler manage error responses

Example:

```typescript
try {
  // Implementation
} catch (error) {
  if (error.code === 'P2002') {
    // Prisma unique constraint error
    return next(new AppError('Resource already exists', 400));
  }
  next(error);
}
```

### WebSocket Events

When adding features that benefit from real-time updates:

1. Update the socket handlers in `src/sockets/index.ts`
2. Emit events when data changes
3. Listen for events on the client side

Example:

```typescript
// In a controller after updating data
io.to('admin-room').emit('inventory-updated', { productId, newQuantity });
```

---

This documentation provides a comprehensive overview of the POS backend system architecture and guidelines for future development. Follow these guidelines to maintain consistency and best practices when extending the system.
