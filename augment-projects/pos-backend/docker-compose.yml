services:
  postgres:
    image: postgres:15-alpine
    container_name: pos_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: pos_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - pos_network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: pos_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pos_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  pgadmin:
    image: dpage/pgadmin4
    container_name: pos_pgadmin
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - pos_network
    restart: unless-stopped
    depends_on:
      - postgres

networks:
  pos_network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:
