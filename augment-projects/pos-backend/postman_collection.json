{"info": {"_postman_id": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "name": "POS Backend API", "description": "Collection for testing the POS Backend API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication related endpoints", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Authenticates a user and returns tokens"}, "response": [{"name": "Login Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"user\": {\n      \"id\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\",\n      \"email\": \"<EMAIL>\",\n      \"firstName\": \"Admin\",\n      \"lastName\": \"User\",\n      \"role\": \"ADMIN\"\n    },\n    \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"refreshToken\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\"\n  }\n}"}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Invalidates the refresh token"}, "response": [{"name": "Logout Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"message\": \"Logged out successfully\"\n}"}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "description": "Get a new access token using a refresh token"}, "response": [{"name": "Refresh <PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"refreshToken\": \"b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7\"\n  }\n}"}]}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"password123\",\n  \"newPassword\": \"newPassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "change-password"]}, "description": "Change the authenticated user's password"}, "response": [{"name": "Change Password Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"password123\",\n  \"newPassword\": \"newPassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "change-password"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"message\": \"Password changed successfully\"\n}"}]}]}, {"name": "Users", "description": "User related endpoints", "item": [{"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Admin\",\n  \"lastName\": \"User\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/update-user", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "update-user"]}, "description": "Update the authenticated user's profile information"}, "response": [{"name": "Update User Success", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Admin\",\n  \"lastName\": \"User\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/update-user", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "update-user"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"user\": {\n      \"id\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\",\n      \"email\": \"<EMAIL>\",\n      \"firstName\": \"Admin\",\n      \"lastName\": \"User\",\n      \"role\": \"ADMIN\"\n    }\n  }\n}"}]}]}, {"name": "Categories", "description": "Category related endpoints", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"]}, "description": "Get all categories"}, "response": [{"name": "Get All Categories Success", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"categories\": [\n      {\n        \"id\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\",\n        \"name\": \"Electronics\",\n        \"description\": \"Electronic devices and accessories\"\n      },\n      {\n        \"id\": \"b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7\",\n        \"name\": \"Clothing\",\n        \"description\": \"Apparel and fashion items\"\n      }\n    ]\n  }\n}"}]}, {"name": "Get Category By ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "description": "Category ID"}]}, "description": "Get a category by its ID"}, "response": [{"name": "Get Category By ID Success", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"category\": {\n      \"id\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\",\n      \"name\": \"Electronics\",\n      \"description\": \"Electronic devices and accessories\"\n    }\n  }\n}"}]}, {"name": "Get Category Products", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories/:id/products", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id", "products"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "description": "Category ID"}]}, "description": "Get all products in a specific category"}, "response": [{"name": "Get Category Products Success", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories/:id/products", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id", "products"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"products\": [\n      {\n        \"id\": \"c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8\",\n        \"name\": \"Smartphone\",\n        \"description\": \"Latest smartphone model\",\n        \"price\": 799.99,\n        \"stock\": 50,\n        \"categoryId\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\"\n      },\n      {\n        \"id\": \"d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9\",\n        \"name\": \"Laptop\",\n        \"description\": \"High-performance laptop\",\n        \"price\": 1299.99,\n        \"stock\": 25,\n        \"categoryId\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\"\n      }\n    ]\n  }\n}"}]}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Home Appliances\",\n  \"description\": \"Household appliances and equipment\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"]}, "description": "Create a new category (Admin/Manager only)"}, "response": [{"name": "Create Category Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Home Appliances\",\n  \"description\": \"Household appliances and equipment\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"category\": {\n      \"id\": \"e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0\",\n      \"name\": \"Home Appliances\",\n      \"description\": \"Household appliances and equipment\"\n    }\n  }\n}"}]}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Category Name\",\n  \"description\": \"Updated category description\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/categories/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "description": "Category ID"}]}, "description": "Update an existing category (Admin/Manager only)"}, "response": [{"name": "Update Category Success", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Category Name\",\n  \"description\": \"Updated category description\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/categories/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"data\": {\n    \"category\": {\n      \"id\": \"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\",\n      \"name\": \"Updated Category Name\",\n      \"description\": \"Updated category description\"\n    }\n  }\n}"}]}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "description": "Category ID"}]}, "description": "Delete a category (Admin/Manager only)"}, "response": [{"name": "Delete Category Success", "originalRequest": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/categories/:id", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", ":id"], "variable": [{"key": "id", "value": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"success\",\n  \"message\": \"Category deleted successfully\"\n}"}]}]}, {"name": "Products", "description": "Product related endpoints", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products?page=1&limit=10&category=category_id&active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "category_id"}, {"key": "active", "value": "true"}]}, "description": "Get a paginated list of products with optional filtering by category and active status"}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/product_id", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "product_id"]}, "description": "Get a single product by its ID"}, "response": []}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Product\",\n  \"description\": \"Product description\",\n  \"barcode\": \"1234567890123\",\n  \"sku\": \"SKU12345\",\n  \"price\": 99.99,\n  \"costPrice\": 49.99,\n  \"categoryId\": \"category_id\",\n  \"stockQuantity\": 100,\n  \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/products", "host": ["{{base_url}}"], "path": ["api", "v1", "products"]}, "description": "Create a new product"}, "response": []}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\",\n  \"description\": \"Updated description\",\n  \"price\": 109.99,\n  \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/products/product_id", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "product_id"]}, "description": "Update an existing product"}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/product_id", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "product_id"]}, "description": "Delete a product by ID. Note: Cannot delete products that are used in transactions."}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/search?query=search_term&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search"], "query": [{"key": "query", "value": "search_term"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Search products by name, description, barcode, or SKU"}, "response": []}, {"name": "Adjust Stock", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 5,\n  \"type\": \"ADDITION\",\n  \"reason\": \"Stock received from supplier\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/products/product_id/adjust-stock", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "product_id", "adjust-stock"]}, "description": "Adjust product stock quantity. Types: ADDITION, SUBTRACTION, INVENTORY_COUNT, DAMAGED, RETURNED"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract tokens from login response", "if (pm.info.requestName === \"Login\" && pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set(\"auth_token\", responseJson.data.token);", "    }", "    if (responseJson.data && responseJson.data.refreshToken) {", "        pm.environment.set(\"refresh_token\", responseJson.data.refreshToken);", "    }", "}", "", "// Auto-extract new tokens from refresh token response", "if (pm.info.requestName === \"Refresh Token\" && pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set(\"auth_token\", responseJson.data.token);", "    }", "    if (responseJson.data && responseJson.data.refreshToken) {", "        pm.environment.set(\"refresh_token\", responseJson.data.refreshToken);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}]}