# POS Backend API

A scalable and modular backend API for a Point of Sale (POS) system built with Node.js, Express, and PostgreSQL.

## Features

- **Authentication**: JWT-based auth with role-based access control
- **Product & Inventory Management**: CRUD operations for products, categories, and stock adjustments
- **Sales & Transactions**: Create invoices, record payments, apply discounts and taxes
- **Reports**: Generate daily sales, product-wise sales, and staff performance reports
- **Customer Management**: Track loyalty points and customer details
- **Configuration Management**: Multi-branch support, tax settings, and more
- **Real-time Updates**: WebSocket support for order and inventory updates

## Tech Stack

- **Runtime**: Node.js
- **API Framework**: Express.js
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Caching**: Redis
- **Real-time**: Socket.IO
- **Authentication**: JWT
- **Type Safety**: TypeScript
- **Testing**: Jest
- **Containerization**: Docker

## Getting Started

### Prerequisites

- Node.js (v16+)
- Docker and Docker Compose
- PostgreSQL (or use the Docker setup)
- Redis (or use the Docker setup)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/pos-backend.git
   cd pos-backend
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the database and Redis using Docker:

   ```bash
   docker-compose up -d
   ```

5. Generate Prisma client:

   ```bash
   npm run prisma:generate
   ```

6. Run database migrations:

   ```bash
   npm run prisma:migrate
   ```

7. Seed the database:

   ```bash
   npm run seed
   ```

8. Start the development server:

   ```bash
   npm run dev
   ```

The API will be available at <http://localhost:3000/api/v1>

### Running Tests

```bash
npm test
```

### Building for Production

```bash
npm run build
npm start
```

## API Documentation

API documentation is available at <http://localhost:3000/api/v1/docs> when the server is running.

## Docker Deployment

Build and run the Docker image:

```bash
docker build -t pos-backend .
docker run -p 3000:3000 --env-file .env pos-backend
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
